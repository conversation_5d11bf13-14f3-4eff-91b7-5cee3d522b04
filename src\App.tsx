import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { CourseProvider } from './contexts/CourseContext';
import { LessonProvider } from './contexts/LessonContext';
import Layout from './components/Layout/Layout';
import LoginPage from './components/Auth/LoginPage';
import StudentDashboard from './components/Dashboard/StudentDashboard';
import AdminDashboard from './components/Admin/AdminDashboard';
import CourseList from './components/Course/CourseList';
import CourseDetail from './components/Course/CourseDetail';
import LessonViewer from './components/Lesson/LessonViewer';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import { UserRole } from './types/index';
import './index.css';

function App() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">LMS Application Test</h1>
        <p className="text-gray-600">If you can see this, React is working!</p>
        <p className="text-sm text-gray-500 mt-2">Tailwind CSS is also working if you see proper styling.</p>
      </div>
    </div>
  );
}

export default App;