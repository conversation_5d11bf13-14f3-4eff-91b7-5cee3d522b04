import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DocumentProvider } from './contexts/DocumentContext';
import Header from './components/Layout/Header';
import Sidebar from './components/Layout/Sidebar';
import Dashboard from './components/Dashboard/Dashboard';
import RichTextEditor from './components/Editor/RichTextEditor';
import CollaboratorList from './components/Editor/CollaboratorList';
import LoginForm from './components/Auth/LoginForm';
import RegisterForm from './components/Auth/RegisterForm';
import { useDocument } from './contexts/DocumentContext';

const AppContent: React.FC = () => {
  const { state: authState } = useAuth();
  const { state: documentState } = useDocument();
  const [isLoginMode, setIsLoginMode] = useState(true);

  if (!authState.user) {
    return isLoginMode ? (
      <LoginForm onToggleMode={() => setIsLoginMode(false)} />
    ) : (
      <RegisterForm onToggleMode={() => setIsLoginMode(true)} />
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 flex overflow-hidden">
          {documentState.activeDocument ? (
            <>
              <div className="flex-1 overflow-hidden">
                <RichTextEditor document={documentState.activeDocument} />
              </div>
              <CollaboratorList />
            </>
          ) : (
            <Dashboard />
          )}
        </main>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <DocumentProvider>
        <AppContent />
      </DocumentProvider>
    </AuthProvider>
  );
};

export default App;