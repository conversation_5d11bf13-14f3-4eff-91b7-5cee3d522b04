import React from 'react';

function App() {
  console.log('App component is rendering!');

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f3f4f6',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '20px'
        }}>
          🎓 Learning Management System - Debug Mode
        </h1>

        <div style={{
          backgroundColor: '#f9fafb',
          padding: '20px',
          borderRadius: '6px',
          marginBottom: '20px'
        }}>
          <h2 style={{ fontSize: '20px', marginBottom: '10px' }}>✅ System Status</h2>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            <li style={{ marginBottom: '8px' }}>✅ React is working</li>
            <li style={{ marginBottom: '8px' }}>✅ JavaScript is executing</li>
            <li style={{ marginBottom: '8px' }}>✅ Styles are applied (inline)</li>
            <li style={{ marginBottom: '8px' }}>✅ Component is rendering</li>
          </ul>
        </div>

        <div style={{
          backgroundColor: '#eff6ff',
          padding: '20px',
          borderRadius: '6px',
          border: '1px solid #dbeafe'
        }}>
          <h3 style={{ fontSize: '18px', marginBottom: '10px' }}>🔧 Debug Information</h3>
          <p style={{ marginBottom: '8px' }}>
            <strong>Current Time:</strong> {new Date().toLocaleString()}
          </p>
          <p style={{ marginBottom: '8px' }}>
            <strong>User Agent:</strong> {navigator.userAgent.substring(0, 50)}...
          </p>
          <p style={{ marginBottom: '8px' }}>
            <strong>Window Size:</strong> {window.innerWidth} x {window.innerHeight}
          </p>
        </div>

        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <p style={{ color: '#6b7280' }}>
            If you can see this page, the basic React application is working correctly!
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;