import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { CourseProvider } from './contexts/CourseContext';
import { LessonProvider } from './contexts/LessonContext';
import Layout from './components/Layout/Layout';
import LoginPage from './components/Auth/LoginPage';
import StudentDashboard from './components/Dashboard/StudentDashboard';
import AdminDashboard from './components/Admin/AdminDashboard';
import CourseList from './components/Course/CourseList';
import CourseDetail from './components/Course/CourseDetail';
import LessonViewer from './components/Lesson/LessonViewer';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import { UserRole } from './types/index';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <CourseProvider>
        <LessonProvider>
          <Router>
            <div className="min-h-screen bg-gray-100">
              <Routes>
                {/* Public Routes */}
                <Route path="/login" element={<LoginPage />} />

                {/* Protected Routes */}
                <Route path="/" element={<Layout />}>
                  <Route index element={<Navigate to="/dashboard" replace />} />

                  {/* Student Routes */}
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <StudentDashboard />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/courses"
                    element={
                      <ProtectedRoute>
                        <CourseList />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/courses/:courseId"
                    element={
                      <ProtectedRoute>
                        <CourseDetail />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/lessons/:lessonId"
                    element={
                      <ProtectedRoute>
                        <LessonViewer />
                      </ProtectedRoute>
                    }
                  />

                  {/* Admin Routes */}
                  <Route
                    path="/admin"
                    element={
                      <ProtectedRoute requiredRole={UserRole.ADMIN}>
                        <AdminDashboard />
                      </ProtectedRoute>
                    }
                  />

                  {/* Instructor Routes */}
                  <Route
                    path="/instructor"
                    element={
                      <ProtectedRoute requiredRole={UserRole.INSTRUCTOR}>
                        <AdminDashboard />
                      </ProtectedRoute>
                    }
                  />
                </Route>

                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          </Router>
        </LessonProvider>
      </CourseProvider>
    </AuthProvider>
  );
}

export default App;