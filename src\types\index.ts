// User Management Types
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export enum UserRole {
  STUDENT = 'student',
  INSTRUCTOR = 'instructor',
  ADMIN = 'admin'
}

// Course Structure Types
export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  instructorId: string;
  instructor: User;
  modules: Module[];
  tags: string[];
  difficulty: CourseDifficulty;
  estimatedDuration: number; // in minutes
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
  prerequisites: string[]; // Course IDs
}

export enum CourseDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export interface Module {
  id: string;
  courseId: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  prerequisites: string[]; // Module IDs
  estimatedDuration: number; // in minutes
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Lesson {
  id: string;
  moduleId: string;
  title: string;
  description: string;
  content: LessonContent;
  order: number;
  type: LessonType;
  estimatedDuration: number; // in minutes
  prerequisites: string[]; // Lesson IDs
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum LessonType {
  TEXT = 'text',
  VIDEO = 'video',
  INTERACTIVE = 'interactive',
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment'
}

export interface LessonContent {
  type: LessonType;
  data: TextContent | VideoContent | InteractiveContent | QuizContent | AssignmentContent;
}

export interface TextContent {
  html: string;
  attachments?: Attachment[];
}

export interface VideoContent {
  videoUrl: string;
  transcript?: string;
  attachments?: Attachment[];
}

export interface InteractiveContent {
  html: string;
  scripts?: string[];
  styles?: string[];
  interactionType?: InteractionType;
}

export enum InteractionType {
  SIMULATION = 'simulation',
  DRAG_DROP = 'drag_drop',
  HOTSPOT = 'hotspot',
  TIMELINE = 'timeline',
  MATCHING = 'matching'
}

export interface QuizContent {
  questions: QuizQuestion[];
  passingScore: number;
  allowRetries: boolean;
  timeLimit?: number; // in minutes
}

export interface QuizQuestion {
  id: string;
  question: string;
  type: QuestionType;
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  points: number;
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay'
}

export interface AssignmentContent {
  instructions: string;
  submissionType: SubmissionType;
  dueDate?: Date;
  maxPoints: number;
  rubric?: AssignmentRubric[];
}

export enum SubmissionType {
  TEXT = 'text',
  FILE = 'file',
  URL = 'url'
}

export interface AssignmentRubric {
  criteria: string;
  maxPoints: number;
  description: string;
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
}

// Progress Tracking Types
export interface UserProgress {
  id: string;
  userId: string;
  courseId: string;
  moduleId?: string;
  lessonId?: string;
  status: ProgressStatus;
  completedAt?: Date;
  score?: number;
  timeSpent: number; // in minutes
  lastAccessedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum ProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface CourseEnrollment {
  id: string;
  userId: string;
  courseId: string;
  enrolledAt: Date;
  completedAt?: Date;
  progress: number; // percentage 0-100
  status: EnrollmentStatus;
}

export enum EnrollmentStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  DROPPED = 'dropped',
  SUSPENDED = 'suspended'
}

// Quiz and Assignment Submission Types
export interface QuizSubmission {
  id: string;
  userId: string;
  lessonId: string;
  answers: QuizAnswer[];
  score: number;
  maxScore: number;
  submittedAt: Date;
  timeSpent: number; // in minutes
  passed: boolean;
}

export interface QuizAnswer {
  questionId: string;
  answer: string | string[];
  isCorrect: boolean;
  points: number;
}

export interface AssignmentSubmission {
  id: string;
  userId: string;
  lessonId: string;
  content?: string;
  fileUrl?: string;
  submittedAt: Date;
  gradedAt?: Date;
  score?: number;
  maxScore: number;
  feedback?: string;
  status: SubmissionStatus;
}

export enum SubmissionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  GRADED = 'graded',
  RETURNED = 'returned'
}

// Application State Types
export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface CourseState {
  courses: Course[];
  activeCourse: Course | null;
  enrollments: CourseEnrollment[];
  isLoading: boolean;
  error: string | null;
}

export interface LessonState {
  activeLesson: Lesson | null;
  progress: UserProgress[];
  submissions: (QuizSubmission | AssignmentSubmission)[];
  isLoading: boolean;
  error: string | null;
}

// Navigation and UI Types
export interface BreadcrumbItem {
  label: string;
  path: string;
  isActive: boolean;
}

export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon?: string;
  children?: NavigationItem[];
  isLocked?: boolean;
  progress?: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and Filter Types
export interface CourseFilters {
  difficulty?: CourseDifficulty;
  tags?: string[];
  instructorId?: string;
  isPublished?: boolean;
  search?: string;
}

export interface ProgressSummary {
  totalCourses: number;
  completedCourses: number;
  inProgressCourses: number;
  totalLessons: number;
  completedLessons: number;
  totalTimeSpent: number; // in minutes
  averageScore: number;
}