export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'editor' | 'viewer';
  isOnline: boolean;
  lastSeen: Date;
}

export interface Document {
  id: string;
  title: string;
  content: string;
  type: 'article' | 'report' | 'proposal';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  collaborators: string[];
  isPublic: boolean;
  version: number;
  tags: string[];
}

export interface DocumentVersion {
  id: string;
  documentId: string;
  content: string;
  version: number;
  createdAt: Date;
  createdBy: string;
  summary: string;
}

export interface CollaboratorCursor {
  userId: string;
  userName: string;
  position: number;
  selection: { start: number; end: number } | null;
  color: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface DocumentState {
  documents: Document[];
  activeDocument: Document | null;
  collaborators: User[];
  cursors: CollaboratorCursor[];
  isLoading: boolean;
  error: string | null;
}