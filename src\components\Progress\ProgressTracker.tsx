import React from 'react';
import { Trophy, Target, Clock, TrendingUp, Award, Star } from 'lucide-react';
import { Course, Module, Lesson, UserProgress, ProgressStatus } from '../../types';
import ProgressBar, { CircularProgress } from './ProgressBar';

interface ProgressTrackerProps {
  course: Course;
  userProgress: UserProgress[];
  showDetailed?: boolean;
  className?: string;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  course,
  userProgress,
  showDetailed = false,
  className = '',
}) => {
  // Calculate overall course progress
  const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
  const completedLessons = userProgress.filter(p => p.status === ProgressStatus.COMPLETED).length;
  const inProgressLessons = userProgress.filter(p => p.status === ProgressStatus.IN_PROGRESS).length;
  const overallProgress = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

  // Calculate time spent
  const totalTimeSpent = userProgress.reduce((total, progress) => total + progress.timeSpent, 0);
  
  // Calculate module progress
  const moduleProgress = course.modules.map(module => {
    const moduleLessons = module.lessons;
    const moduleCompletedLessons = moduleLessons.filter(lesson => 
      userProgress.find(p => p.lessonId === lesson.id && p.status === ProgressStatus.COMPLETED)
    ).length;
    const moduleProgressPercentage = moduleLessons.length > 0 ? 
      (moduleCompletedLessons / moduleLessons.length) * 100 : 0;
    
    return {
      module,
      completed: moduleCompletedLessons,
      total: moduleLessons.length,
      percentage: moduleProgressPercentage
    };
  });

  // Calculate achievements
  const achievements = {
    firstLesson: completedLessons > 0,
    halfwayPoint: overallProgress >= 50,
    almostThere: overallProgress >= 80,
    completed: overallProgress >= 100,
    timeSpent: totalTimeSpent >= 60, // 1 hour
    consistent: userProgress.length >= 5 // Engaged with 5+ lessons
  };

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const getProgressColor = (percentage: number): 'blue' | 'green' | 'yellow' | 'red' => {
    if (percentage >= 100) return 'green';
    if (percentage >= 75) return 'blue';
    if (percentage >= 50) return 'yellow';
    return 'red';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Progress Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">Course Progress</h3>
            <p className="text-gray-600">{course.title}</p>
          </div>
          <CircularProgress 
            progress={overallProgress} 
            size={80} 
            color={getProgressColor(overallProgress)}
          />
        </div>

        {/* Progress Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <Target className="w-6 h-6 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-blue-900">{completedLessons}/{totalLessons}</p>
            <p className="text-xs text-blue-600">Lessons Completed</p>
          </div>
          
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <Clock className="w-6 h-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-900">{formatTime(totalTimeSpent)}</p>
            <p className="text-xs text-green-600">Time Spent</p>
          </div>
          
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <TrendingUp className="w-6 h-6 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-yellow-900">{inProgressLessons}</p>
            <p className="text-xs text-yellow-600">In Progress</p>
          </div>
          
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <Trophy className="w-6 h-6 text-purple-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-purple-900">
              {Object.values(achievements).filter(Boolean).length}/6
            </p>
            <p className="text-xs text-purple-600">Achievements</p>
          </div>
        </div>

        {/* Overall Progress Bar */}
        <div className="mt-6">
          <ProgressBar
            progress={overallProgress}
            total={totalLessons}
            completed={completedLessons}
            label="Overall Course Progress"
            showStats={true}
            color={getProgressColor(overallProgress)}
            size="lg"
          />
        </div>
      </div>

      {/* Module Progress */}
      {showDetailed && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Module Progress</h4>
          <div className="space-y-4">
            {moduleProgress.map((moduleData, index) => (
              <div key={moduleData.module.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h5 className="font-medium text-gray-900">
                      Module {index + 1}: {moduleData.module.title}
                    </h5>
                    <p className="text-sm text-gray-600">{moduleData.module.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {Math.round(moduleData.percentage)}%
                    </p>
                    <p className="text-xs text-gray-600">
                      {moduleData.completed}/{moduleData.total} lessons
                    </p>
                  </div>
                </div>
                
                <ProgressBar
                  progress={moduleData.percentage}
                  color={getProgressColor(moduleData.percentage)}
                  size="sm"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Achievements */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Achievements</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className={`p-3 rounded-lg border-2 transition-colors ${
            achievements.firstLesson 
              ? 'border-green-200 bg-green-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <Star className={`w-5 h-5 ${
                achievements.firstLesson ? 'text-green-600' : 'text-gray-400'
              }`} />
              <span className={`text-sm font-medium ${
                achievements.firstLesson ? 'text-green-900' : 'text-gray-500'
              }`}>
                First Steps
              </span>
            </div>
            <p className="text-xs text-gray-600">Complete your first lesson</p>
          </div>

          <div className={`p-3 rounded-lg border-2 transition-colors ${
            achievements.halfwayPoint 
              ? 'border-blue-200 bg-blue-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <Target className={`w-5 h-5 ${
                achievements.halfwayPoint ? 'text-blue-600' : 'text-gray-400'
              }`} />
              <span className={`text-sm font-medium ${
                achievements.halfwayPoint ? 'text-blue-900' : 'text-gray-500'
              }`}>
                Halfway There
              </span>
            </div>
            <p className="text-xs text-gray-600">Complete 50% of the course</p>
          </div>

          <div className={`p-3 rounded-lg border-2 transition-colors ${
            achievements.almostThere 
              ? 'border-yellow-200 bg-yellow-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className={`w-5 h-5 ${
                achievements.almostThere ? 'text-yellow-600' : 'text-gray-400'
              }`} />
              <span className={`text-sm font-medium ${
                achievements.almostThere ? 'text-yellow-900' : 'text-gray-500'
              }`}>
                Almost There
              </span>
            </div>
            <p className="text-xs text-gray-600">Complete 80% of the course</p>
          </div>

          <div className={`p-3 rounded-lg border-2 transition-colors ${
            achievements.completed 
              ? 'border-green-200 bg-green-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <Trophy className={`w-5 h-5 ${
                achievements.completed ? 'text-green-600' : 'text-gray-400'
              }`} />
              <span className={`text-sm font-medium ${
                achievements.completed ? 'text-green-900' : 'text-gray-500'
              }`}>
                Course Master
              </span>
            </div>
            <p className="text-xs text-gray-600">Complete the entire course</p>
          </div>

          <div className={`p-3 rounded-lg border-2 transition-colors ${
            achievements.timeSpent 
              ? 'border-purple-200 bg-purple-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <Clock className={`w-5 h-5 ${
                achievements.timeSpent ? 'text-purple-600' : 'text-gray-400'
              }`} />
              <span className={`text-sm font-medium ${
                achievements.timeSpent ? 'text-purple-900' : 'text-gray-500'
              }`}>
                Dedicated Learner
              </span>
            </div>
            <p className="text-xs text-gray-600">Spend 1+ hours learning</p>
          </div>

          <div className={`p-3 rounded-lg border-2 transition-colors ${
            achievements.consistent 
              ? 'border-indigo-200 bg-indigo-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <Award className={`w-5 h-5 ${
                achievements.consistent ? 'text-indigo-600' : 'text-gray-400'
              }`} />
              <span className={`text-sm font-medium ${
                achievements.consistent ? 'text-indigo-900' : 'text-gray-500'
              }`}>
                Engaged Student
              </span>
            </div>
            <p className="text-xs text-gray-600">Engage with 5+ lessons</p>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      {overallProgress < 100 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-blue-900 mb-2">Keep Going!</h4>
          <p className="text-blue-800 mb-4">
            You're {Math.round(100 - overallProgress)}% away from completing this course.
          </p>
          <div className="space-y-2">
            {inProgressLessons > 0 && (
              <p className="text-sm text-blue-700">
                • Continue with {inProgressLessons} lesson{inProgressLessons !== 1 ? 's' : ''} in progress
              </p>
            )}
            <p className="text-sm text-blue-700">
              • {totalLessons - completedLessons} lesson{totalLessons - completedLessons !== 1 ? 's' : ''} remaining
            </p>
            <p className="text-sm text-blue-700">
              • Estimated time to completion: {formatTime((totalLessons - completedLessons) * 15)} 
              <span className="text-xs"> (assuming 15 min per lesson)</span>
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressTracker;
