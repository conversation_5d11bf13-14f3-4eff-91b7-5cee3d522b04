import React, { useState } from 'react';
import { 
  Search, 
  BookOpen, 
  Clock, 
  Star, 
  Download, 
  Calendar,
  Target,
  Award,
  Filter,
  ChevronDown,
  ExternalLink
} from 'lucide-react';
import { Course, CourseEnrollment, UserProgress, ProgressStatus, CourseDifficulty } from '../../types';
import { useCourse } from '../../contexts/CourseContext';
import { useLesson } from '../../contexts/LessonContext';

interface QuickActionsProps {
  onCourseSelect?: (courseId: string) => void;
  onLessonSelect?: (lessonId: string) => void;
  className?: string;
}

const QuickActions: React.FC<QuickActionsProps> = ({
  onCourseSelect,
  onLessonSelect,
  className = '',
}) => {
  const { state: courseState, actions: courseActions } = useCourse();
  const { state: lessonState } = useLesson();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [difficultyFilter, setDifficultyFilter] = useState<CourseDifficulty | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'enrolled' | 'available' | 'completed'>('all');

  // Get available courses (not enrolled)
  const availableCourses = courseState.courses.filter(course => 
    !courseState.enrollments.some(enrollment => enrollment.courseId === course.id)
  );

  // Get enrolled courses
  const enrolledCourses = courseState.enrollments.map(enrollment => {
    const course = courseState.courses.find(c => c.id === enrollment.courseId);
    return course ? { course, enrollment } : null;
  }).filter(Boolean);

  // Filter courses based on search and filters
  const filterCourses = (courses: Course[]) => {
    return courses.filter(course => {
      // Search filter
      if (searchQuery && !course.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !course.description.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !course.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))) {
        return false;
      }

      // Difficulty filter
      if (difficultyFilter !== 'all' && course.difficulty !== difficultyFilter) {
        return false;
      }

      return true;
    });
  };

  const filteredAvailableCourses = filterCourses(availableCourses);
  const filteredEnrolledCourses = enrolledCourses.filter(item => {
    if (!item) return false;
    
    // Apply search and difficulty filters
    if (!filterCourses([item.course]).length) return false;
    
    // Status filter
    if (statusFilter === 'completed' && item.enrollment.progress < 100) return false;
    if (statusFilter === 'enrolled' && (item.enrollment.progress === 0 || item.enrollment.progress >= 100)) return false;
    
    return true;
  });

  // Get recent lessons for quick access
  const getRecentLessons = () => {
    return lessonState.progress
      .filter(p => p.lastAccessed)
      .sort((a, b) => (b.lastAccessed?.getTime() || 0) - (a.lastAccessed?.getTime() || 0))
      .slice(0, 5)
      .map(progress => {
        const course = courseState.courses.find(c => 
          c.modules.some(m => m.lessons.some(l => l.id === progress.lessonId))
        );
        const lesson = course?.modules
          .flatMap(m => m.lessons)
          .find(l => l.id === progress.lessonId);
        
        return lesson && course ? { lesson, course, progress } : null;
      })
      .filter(Boolean);
  };

  const recentLessons = getRecentLessons();

  const handleEnroll = async (courseId: string) => {
    try {
      await courseActions.enrollInCourse(courseId);
    } catch (error) {
      console.error('Failed to enroll in course:', error);
    }
  };

  const getDifficultyColor = (difficulty: CourseDifficulty) => {
    switch (difficulty) {
      case CourseDifficulty.BEGINNER:
        return 'bg-green-100 text-green-800';
      case CourseDifficulty.INTERMEDIATE:
        return 'bg-yellow-100 text-yellow-800';
      case CourseDifficulty.ADVANCED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search courses, lessons, or topics..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
              <select
                value={difficultyFilter}
                onChange={(e) => setDifficultyFilter(e.target.value as CourseDifficulty | 'all')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Levels</option>
                <option value={CourseDifficulty.BEGINNER}>Beginner</option>
                <option value={CourseDifficulty.INTERMEDIATE}>Intermediate</option>
                <option value={CourseDifficulty.ADVANCED}>Advanced</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Courses</option>
                <option value="enrolled">In Progress</option>
                <option value="completed">Completed</option>
                <option value="available">Available to Enroll</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Recent Lessons */}
      {recentLessons.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Continue Learning</h3>
          <div className="space-y-3">
            {recentLessons.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                onClick={() => onLessonSelect?.(item!.lesson.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    item!.progress.status === ProgressStatus.COMPLETED 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-blue-100 text-blue-600'
                  }`}>
                    {item!.progress.status === ProgressStatus.COMPLETED ? (
                      <Award className="w-4 h-4" />
                    ) : (
                      <Clock className="w-4 h-4" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{item!.lesson.title}</p>
                    <p className="text-sm text-gray-600">{item!.course.title}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">{formatTime(item!.progress.timeSpent)} spent</p>
                  <p className={`text-xs font-medium ${
                    item!.progress.status === ProgressStatus.COMPLETED ? 'text-green-600' : 'text-blue-600'
                  }`}>
                    {item!.progress.status === ProgressStatus.COMPLETED ? 'Completed' : 'Continue'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Enrolled Courses */}
      {filteredEnrolledCourses.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Courses</h3>
          <div className="space-y-4">
            {filteredEnrolledCourses.map((item) => (
              <div
                key={item!.course.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onCourseSelect?.(item!.course.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900">{item!.course.title}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(item!.course.difficulty)}`}>
                        {item!.course.difficulty}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{item!.course.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{Math.round(item!.enrollment.progress)}% complete</span>
                      <span>{item!.course.estimatedDuration} min total</span>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span>{item!.course.rating}</span>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="w-16 h-2 bg-gray-200 rounded-full">
                      <div 
                        className="h-2 bg-blue-600 rounded-full transition-all duration-300"
                        style={{ width: `${item!.enrollment.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Courses */}
      {filteredAvailableCourses.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Courses</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredAvailableCourses.slice(0, 6).map((course) => (
              <div
                key={course.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900">{course.title}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(course.difficulty)}`}>
                        {course.difficulty}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{course.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{course.estimatedDuration} min</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span>{course.rating}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BookOpen className="w-4 h-4" />
                        <span>{course.modules.reduce((total, module) => total + module.lessons.length, 0)} lessons</span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {course.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => onCourseSelect?.(course.id)}
                    className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    <span>View Details</span>
                    <ExternalLink className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleEnroll(course.id)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                  >
                    Enroll Now
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          {filteredAvailableCourses.length > 6 && (
            <div className="mt-4 text-center">
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View {filteredAvailableCourses.length - 6} more courses
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {searchQuery && filteredAvailableCourses.length === 0 && filteredEnrolledCourses.length === 0 && (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-gray-500">No courses found matching your search.</p>
          <p className="text-sm text-gray-400 mt-1">Try adjusting your search terms or filters.</p>
        </div>
      )}
    </div>
  );
};

export default QuickActions;
