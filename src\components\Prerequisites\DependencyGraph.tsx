import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Clock, Target, ArrowRight, Maximize2, Minimize2 } from 'lucide-react';
import { Course, Module, Lesson, UserProgress, ProgressStatus } from '../../types';

interface DependencyNode {
  id: string;
  title: string;
  type: 'module' | 'lesson';
  status: 'completed' | 'in-progress' | 'available' | 'locked';
  prerequisites: string[];
  dependents: string[];
  level: number;
  position: { x: number; y: number };
}

interface DependencyGraphProps {
  course: Course;
  userProgress: UserProgress[];
  currentItemId?: string;
  onNodeClick?: (id: string, type: 'module' | 'lesson') => void;
  showModulesOnly?: boolean;
  className?: string;
}

const DependencyGraph: React.FC<DependencyGraphProps> = ({
  course,
  userProgress,
  currentItemId,
  onNodeClick,
  showModulesOnly = false,
  className = '',
}) => {
  const [nodes, setNodes] = useState<DependencyNode[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  useEffect(() => {
    buildDependencyGraph();
  }, [course, userProgress, showModulesOnly]);

  const buildDependencyGraph = () => {
    const allNodes: DependencyNode[] = [];
    const nodeMap = new Map<string, DependencyNode>();

    // Helper function to get item status
    const getItemStatus = (id: string, type: 'module' | 'lesson') => {
      if (type === 'lesson') {
        const progress = userProgress.find(p => p.lessonId === id);
        if (progress?.status === ProgressStatus.COMPLETED) return 'completed';
        if (progress?.status === ProgressStatus.IN_PROGRESS) return 'in-progress';
        
        // Check if prerequisites are met
        const lesson = course.modules.flatMap(m => m.lessons).find(l => l.id === id);
        if (lesson && lesson.prerequisites) {
          const prerequisitesMet = lesson.prerequisites.every(prereqId => {
            const prereqProgress = userProgress.find(p => p.lessonId === prereqId);
            return prereqProgress?.status === ProgressStatus.COMPLETED;
          });
          return prerequisitesMet ? 'available' : 'locked';
        }
        return 'available';
      } else {
        // Module status
        const module = course.modules.find(m => m.id === id);
        if (!module) return 'locked';
        
        const completedLessons = module.lessons.filter(lesson => {
          const progress = userProgress.find(p => p.lessonId === lesson.id);
          return progress?.status === ProgressStatus.COMPLETED;
        }).length;
        
        if (completedLessons === module.lessons.length && module.lessons.length > 0) {
          return 'completed';
        } else if (completedLessons > 0) {
          return 'in-progress';
        }
        
        // Check module prerequisites
        if (module.prerequisites) {
          const prerequisitesMet = module.prerequisites.every(prereqId => {
            const prereqModule = course.modules.find(m => m.id === prereqId);
            if (prereqModule) {
              const prereqCompleted = prereqModule.lessons.every(lesson => {
                const progress = userProgress.find(p => p.lessonId === lesson.id);
                return progress?.status === ProgressStatus.COMPLETED;
              });
              return prereqCompleted;
            }
            return false;
          });
          return prerequisitesMet ? 'available' : 'locked';
        }
        return 'available';
      }
    };

    // Add modules
    course.modules.forEach((module, moduleIndex) => {
      const node: DependencyNode = {
        id: module.id,
        title: module.title,
        type: 'module',
        status: getItemStatus(module.id, 'module') as any,
        prerequisites: module.prerequisites || [],
        dependents: [],
        level: 0,
        position: { x: 0, y: 0 }
      };
      allNodes.push(node);
      nodeMap.set(module.id, node);

      // Add lessons if not showing modules only
      if (!showModulesOnly) {
        module.lessons.forEach((lesson, lessonIndex) => {
          const lessonNode: DependencyNode = {
            id: lesson.id,
            title: lesson.title,
            type: 'lesson',
            status: getItemStatus(lesson.id, 'lesson') as any,
            prerequisites: lesson.prerequisites || [],
            dependents: [],
            level: 0,
            position: { x: 0, y: 0 }
          };
          allNodes.push(lessonNode);
          nodeMap.set(lesson.id, lessonNode);
        });
      }
    });

    // Build dependency relationships
    allNodes.forEach(node => {
      node.prerequisites.forEach(prereqId => {
        const prereqNode = nodeMap.get(prereqId);
        if (prereqNode) {
          prereqNode.dependents.push(node.id);
        }
      });
    });

    // Calculate levels (topological sort)
    const calculateLevels = () => {
      const visited = new Set<string>();
      const levels = new Map<string, number>();

      const dfs = (nodeId: string): number => {
        if (visited.has(nodeId)) {
          return levels.get(nodeId) || 0;
        }

        visited.add(nodeId);
        const node = nodeMap.get(nodeId);
        if (!node) return 0;

        let maxPrereqLevel = -1;
        node.prerequisites.forEach(prereqId => {
          const prereqLevel = dfs(prereqId);
          maxPrereqLevel = Math.max(maxPrereqLevel, prereqLevel);
        });

        const level = maxPrereqLevel + 1;
        levels.set(nodeId, level);
        node.level = level;
        return level;
      };

      allNodes.forEach(node => {
        if (!visited.has(node.id)) {
          dfs(node.id);
        }
      });
    };

    calculateLevels();

    // Position nodes
    const levelGroups = new Map<number, DependencyNode[]>();
    allNodes.forEach(node => {
      const level = node.level;
      if (!levelGroups.has(level)) {
        levelGroups.set(level, []);
      }
      levelGroups.get(level)!.push(node);
    });

    const nodeWidth = 200;
    const nodeHeight = 80;
    const levelSpacing = 250;
    const nodeSpacing = 100;

    levelGroups.forEach((nodesInLevel, level) => {
      const totalHeight = (nodesInLevel.length - 1) * (nodeHeight + nodeSpacing);
      const startY = -totalHeight / 2;

      nodesInLevel.forEach((node, index) => {
        node.position = {
          x: level * levelSpacing,
          y: startY + index * (nodeHeight + nodeSpacing)
        };
      });
    });

    setNodes(allNodes);
  };

  const getNodeIcon = (status: string, isCurrent: boolean = false) => {
    if (isCurrent) {
      return <Target className="w-5 h-5 text-blue-600" />;
    }
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'in-progress':
        return <Clock className="w-5 h-5 text-blue-600" />;
      case 'available':
        return <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />;
      case 'locked':
        return <Lock className="w-5 h-5 text-gray-400" />;
      default:
        return <div className="w-5 h-5 border-2 border-gray-300 rounded-full" />;
    }
  };

  const getNodeStyles = (status: string, isCurrent: boolean = false) => {
    if (isCurrent) {
      return 'bg-blue-100 border-blue-300 text-blue-900 shadow-lg';
    }
    
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-300 text-green-900';
      case 'in-progress':
        return 'bg-blue-50 border-blue-300 text-blue-900';
      case 'available':
        return 'bg-white border-gray-300 text-gray-900 hover:bg-gray-50 cursor-pointer';
      case 'locked':
        return 'bg-gray-50 border-gray-300 text-gray-500';
      default:
        return 'bg-white border-gray-300 text-gray-900';
    }
  };

  const handleNodeClick = (node: DependencyNode) => {
    setSelectedNode(selectedNode === node.id ? null : node.id);
    if (node.status === 'available' || node.status === 'in-progress' || node.status === 'completed') {
      onNodeClick?.(node.id, node.type);
    }
  };

  // Calculate SVG dimensions
  const padding = 50;
  const minX = Math.min(...nodes.map(n => n.position.x)) - padding;
  const maxX = Math.max(...nodes.map(n => n.position.x)) + 200 + padding;
  const minY = Math.min(...nodes.map(n => n.position.y)) - padding;
  const maxY = Math.max(...nodes.map(n => n.position.y)) + 80 + padding;
  const svgWidth = maxX - minX;
  const svgHeight = maxY - minY;

  return (
    <div className={`bg-white rounded-lg shadow-md ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            {showModulesOnly ? 'Module Dependencies' : 'Learning Dependencies'}
          </h3>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
          >
            {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            <span>{isExpanded ? 'Collapse' : 'Expand'}</span>
          </button>
        </div>
      </div>

      <div className={`overflow-auto ${isExpanded ? 'h-96' : 'h-64'}`}>
        <svg
          width={svgWidth}
          height={svgHeight}
          viewBox={`${minX} ${minY} ${svgWidth} ${svgHeight}`}
          className="w-full h-full"
        >
          {/* Render connections */}
          {nodes.map(node => 
            node.prerequisites.map(prereqId => {
              const prereqNode = nodes.find(n => n.id === prereqId);
              if (!prereqNode) return null;

              const startX = prereqNode.position.x + 200;
              const startY = prereqNode.position.y + 40;
              const endX = node.position.x;
              const endY = node.position.y + 40;

              return (
                <g key={`${prereqId}-${node.id}`}>
                  <line
                    x1={startX}
                    y1={startY}
                    x2={endX}
                    y2={endY}
                    stroke="#d1d5db"
                    strokeWidth="2"
                    markerEnd="url(#arrowhead)"
                  />
                </g>
              );
            })
          )}

          {/* Arrow marker definition */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="#d1d5db"
              />
            </marker>
          </defs>

          {/* Render nodes */}
          {nodes.map(node => {
            const isCurrent = node.id === currentItemId;
            const isSelected = node.id === selectedNode;
            
            return (
              <g key={node.id}>
                <foreignObject
                  x={node.position.x}
                  y={node.position.y}
                  width="200"
                  height="80"
                >
                  <div
                    className={`w-full h-full p-3 border-2 rounded-lg transition-all ${
                      getNodeStyles(node.status, isCurrent)
                    } ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
                    onClick={() => handleNodeClick(node)}
                  >
                    <div className="flex items-start space-x-2">
                      {getNodeIcon(node.status, isCurrent)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{node.title}</p>
                        <p className="text-xs opacity-75 capitalize">{node.type}</p>
                        <p className="text-xs opacity-75 capitalize mt-1">{node.status}</p>
                      </div>
                    </div>
                  </div>
                </foreignObject>
              </g>
            );
          })}
        </svg>
      </div>

      {/* Legend */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Completed</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4 text-blue-600" />
              <span>In Progress</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-4 h-4 border-2 border-gray-400 rounded-full" />
              <span>Available</span>
            </div>
            <div className="flex items-center space-x-1">
              <Lock className="w-4 h-4 text-gray-400" />
              <span>Locked</span>
            </div>
          </div>
          <div className="flex items-center space-x-1 text-gray-600">
            <ArrowRight className="w-4 h-4" />
            <span>Dependencies</span>
          </div>
        </div>
      </div>

      {/* Selected node details */}
      {selectedNode && (
        <div className="p-4 border-t border-gray-200 bg-blue-50">
          {(() => {
            const node = nodes.find(n => n.id === selectedNode);
            if (!node) return null;
            
            return (
              <div>
                <h4 className="font-medium text-blue-900 mb-2">{node.title}</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>Type: {node.type}</p>
                  <p>Status: {node.status}</p>
                  {node.prerequisites.length > 0 && (
                    <p>Prerequisites: {node.prerequisites.length}</p>
                  )}
                  {node.dependents.length > 0 && (
                    <p>Unlocks: {node.dependents.length} item{node.dependents.length !== 1 ? 's' : ''}</p>
                  )}
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default DependencyGraph;
