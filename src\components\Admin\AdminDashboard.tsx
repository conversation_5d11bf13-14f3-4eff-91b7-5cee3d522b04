import React, { useState } from 'react';
import { 
  Plus, 
  BookOpen, 
  Users, 
  BarChart3, 
  Settings, 
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  Calendar,
  TrendingUp,
  Award,
  Clock
} from 'lucide-react';
import { Course, User, UserProgress, ProgressStatus, UserRole } from '../../types';
import { useCourse } from '../../contexts/CourseContext';
import { useLesson } from '../../contexts/LessonContext';
import { useAuth } from '../../contexts/AuthContext';
import CourseEditor from './CourseEditor';
import LessonEditor from './LessonEditor';

interface AdminDashboardProps {
  className?: string;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({
  className = '',
}) => {
  const { user, hasPermission } = useAuth();
  const { state: courseState, actions: courseActions } = useCourse();
  const { state: lessonState } = useLesson();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'courses' | 'students' | 'analytics'>('overview');
  const [showCourseEditor, setShowCourseEditor] = useState(false);
  const [editingCourseId, setEditingCourseId] = useState<string | null>(null);
  const [showLessonEditor, setShowLessonEditor] = useState(false);
  const [editingLesson, setEditingLesson] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Check permissions
  if (!hasPermission('MANAGE_COURSES')) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <div className="text-red-600 mb-4">
          <Settings className="w-12 h-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-600">You don't have permission to access the admin dashboard.</p>
      </div>
    );
  }

  // Calculate statistics
  const totalCourses = courseState.courses.length;
  const publishedCourses = courseState.courses.filter(c => c.isPublished).length;
  const totalEnrollments = courseState.enrollments.length;
  const totalStudents = new Set(courseState.enrollments.map(e => e.userId)).size;
  const completedLessons = lessonState.progress.filter(p => p.status === ProgressStatus.COMPLETED).length;
  const totalLessons = courseState.courses.reduce((total, course) => 
    total + course.modules.reduce((moduleTotal, module) => moduleTotal + module.lessons.length, 0), 0
  );

  const handleCreateCourse = () => {
    setEditingCourseId(null);
    setShowCourseEditor(true);
  };

  const handleEditCourse = (courseId: string) => {
    setEditingCourseId(courseId);
    setShowCourseEditor(true);
  };

  const handleDeleteCourse = async (courseId: string) => {
    if (window.confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      try {
        await courseActions.deleteCourse(courseId);
      } catch (error) {
        console.error('Failed to delete course:', error);
      }
    }
  };

  const handleSaveCourse = (course: Course) => {
    setShowCourseEditor(false);
    setEditingCourseId(null);
  };

  const handleEditLesson = (lesson: any) => {
    setEditingLesson(lesson);
    setShowLessonEditor(true);
  };

  const handleSaveLesson = (lesson: any) => {
    setShowLessonEditor(false);
    setEditingLesson(null);
    // Update lesson in course
    // This would typically involve updating the course with the modified lesson
  };

  const filteredCourses = courseState.courses.filter(course =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    course.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    course.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  if (showCourseEditor) {
    return (
      <CourseEditor
        courseId={editingCourseId || undefined}
        onSave={handleSaveCourse}
        onCancel={() => setShowCourseEditor(false)}
        className={className}
      />
    );
  }

  if (showLessonEditor && editingLesson) {
    return (
      <LessonEditor
        lesson={editingLesson}
        onSave={handleSaveLesson}
        onCancel={() => setShowLessonEditor(false)}
        className={className}
      />
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Admin Dashboard</h1>
            <p className="text-purple-100">
              Manage courses, track student progress, and analyze learning outcomes
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{totalCourses}</div>
            <div className="text-sm text-purple-100">Total Courses</div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{publishedCourses}</p>
              <p className="text-sm text-gray-600">Published Courses</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{totalStudents}</p>
              <p className="text-sm text-gray-600">Active Students</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{totalEnrollments}</p>
              <p className="text-sm text-gray-600">Total Enrollments</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Award className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">
                {totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0}%
              </p>
              <p className="text-sm text-gray-600">Completion Rate</p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'courses', label: 'Courses', icon: BookOpen },
              { id: 'students', label: 'Students', icon: Users },
              { id: 'analytics', label: 'Analytics', icon: TrendingUp }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Activity */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {lessonState.progress
                      .filter(p => p.lastAccessed)
                      .sort((a, b) => (b.lastAccessed?.getTime() || 0) - (a.lastAccessed?.getTime() || 0))
                      .slice(0, 5)
                      .map((progress, index) => {
                        const course = courseState.courses.find(c => 
                          c.modules.some(m => m.lessons.some(l => l.id === progress.lessonId))
                        );
                        const lesson = course?.modules
                          .flatMap(m => m.lessons)
                          .find(l => l.id === progress.lessonId);
                        
                        if (!course || !lesson) return null;

                        return (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-full ${
                                progress.status === ProgressStatus.COMPLETED 
                                  ? 'bg-green-100 text-green-600' 
                                  : 'bg-blue-100 text-blue-600'
                              }`}>
                                {progress.status === ProgressStatus.COMPLETED ? (
                                  <Award className="w-4 h-4" />
                                ) : (
                                  <Clock className="w-4 h-4" />
                                )}
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{lesson.title}</p>
                                <p className="text-sm text-gray-600">{course.title}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-500">
                                {progress.lastAccessed ? formatDate(progress.lastAccessed) : 'Unknown'}
                              </p>
                              <p className={`text-sm font-medium ${
                                progress.status === ProgressStatus.COMPLETED ? 'text-green-600' : 'text-blue-600'
                              }`}>
                                {progress.status === ProgressStatus.COMPLETED ? 'Completed' : 'In Progress'}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>

                {/* Popular Courses */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Courses</h3>
                  <div className="space-y-3">
                    {courseState.courses
                      .sort((a, b) => b.enrollmentCount - a.enrollmentCount)
                      .slice(0, 5)
                      .map((course) => (
                        <div key={course.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{course.title}</p>
                            <p className="text-sm text-gray-600">{course.enrollmentCount} enrollments</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">★ {course.rating}</span>
                            <button
                              onClick={() => handleEditCourse(course.id)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'courses' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search courses..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button className="flex items-center space-x-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                    <Filter className="w-4 h-4" />
                    <span>Filter</span>
                  </button>
                </div>
                <button
                  onClick={handleCreateCourse}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>Create Course</span>
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredCourses.map((course) => (
                  <div key={course.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-2">{course.title}</h4>
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{course.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{course.modules.length} modules</span>
                          <span>{course.enrollmentCount} students</span>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            course.isPublished 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {course.isPublished ? 'Published' : 'Draft'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        Updated {formatDate(course.updatedAt)}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditCourse(course.id)}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit course"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                          title="View course"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteCourse(course.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete course"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredCourses.length === 0 && (
                <div className="text-center py-12">
                  <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first course.'}
                  </p>
                  {!searchQuery && (
                    <button
                      onClick={handleCreateCourse}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Create Course</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'students' && (
            <div className="text-center py-12">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Student Management</h3>
              <p className="text-gray-600">Student management interface would be implemented here.</p>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Dashboard</h3>
              <p className="text-gray-600">Detailed analytics and reporting would be implemented here.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
