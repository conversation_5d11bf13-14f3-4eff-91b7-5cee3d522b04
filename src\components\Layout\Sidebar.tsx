import React, { useState } from 'react';
import { 
  FileText, 
  Plus, 
  Folder, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  Setting<PERSON>,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { useDocument } from '../../contexts/DocumentContext';
import { Document } from '../../types';

const Sidebar: React.FC = () => {
  const { state: documentState, createDocument, setActiveDocument } = useDocument();
  const [expandedSections, setExpandedSections] = useState<string[]>(['recent', 'documents']);
  const [showCreateMenu, setShowCreateMenu] = useState(false);

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const handleCreateDocument = async (type: Document['type']) => {
    const title = `New ${type.charAt(0).toUpperCase() + type.slice(1)}`;
    await createDocument(title, type);
    setShowCreateMenu(false);
  };

  const getDocumentIcon = (type: Document['type']) => {
    switch (type) {
      case 'article':
        return <FileText className="h-4 w-4" />;
      case 'report':
        return <FileText className="h-4 w-4" />;
      case 'proposal':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="w-64 bg-gray-50 border-r border-gray-200 h-full overflow-y-auto">
      <div className="p-4">
        <div className="relative">
          <button
            onClick={() => setShowCreateMenu(!showCreateMenu)}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Document
          </button>

          {showCreateMenu && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <button
                onClick={() => handleCreateDocument('article')}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-t-md"
              >
                Article
              </button>
              <button
                onClick={() => handleCreateDocument('report')}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              >
                Report
              </button>
              <button
                onClick={() => handleCreateDocument('proposal')}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-b-md"
              >
                Proposal
              </button>
            </div>
          )}
        </div>
      </div>

      <nav className="px-4 space-y-1">
        <div>
          <button
            onClick={() => toggleSection('recent')}
            className="w-full flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100"
          >
            {expandedSections.includes('recent') ? (
              <ChevronDown className="h-4 w-4 mr-1" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1" />
            )}
            <Clock className="h-4 w-4 mr-2" />
            Recent
          </button>
          
          {expandedSections.includes('recent') && (
            <div className="ml-6 space-y-1">
              {documentState.documents.slice(0, 3).map((doc) => (
                <button
                  key={doc.id}
                  onClick={() => setActiveDocument(doc)}
                  className="w-full flex items-center px-2 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-md"
                >
                  {getDocumentIcon(doc.type)}
                  <span className="ml-2 truncate">{doc.title}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        <div>
          <button
            onClick={() => toggleSection('documents')}
            className="w-full flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100"
          >
            {expandedSections.includes('documents') ? (
              <ChevronDown className="h-4 w-4 mr-1" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1" />
            )}
            <Folder className="h-4 w-4 mr-2" />
            All Documents
          </button>
          
          {expandedSections.includes('documents') && (
            <div className="ml-6 space-y-1">
              {documentState.documents.map((doc) => (
                <button
                  key={doc.id}
                  onClick={() => setActiveDocument(doc)}
                  className={`w-full flex items-center justify-between px-2 py-2 text-sm rounded-md transition-colors ${
                    documentState.activeDocument?.id === doc.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center">
                    {getDocumentIcon(doc.type)}
                    <span className="ml-2 truncate">{doc.title}</span>
                  </div>
                  <span className="text-xs text-gray-400">
                    {formatDate(doc.updatedAt)}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>

        <div>
          <button
            onClick={() => toggleSection('shared')}
            className="w-full flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100"
          >
            {expandedSections.includes('shared') ? (
              <ChevronDown className="h-4 w-4 mr-1" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1" />
            )}
            <Users className="h-4 w-4 mr-2" />
            Shared with me
          </button>
          
          {expandedSections.includes('shared') && (
            <div className="ml-6 space-y-1">
              <div className="px-2 py-2 text-sm text-gray-400">
                No shared documents
              </div>
            </div>
          )}
        </div>

        <div>
          <button
            onClick={() => toggleSection('starred')}
            className="w-full flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100"
          >
            {expandedSections.includes('starred') ? (
              <ChevronDown className="h-4 w-4 mr-1" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1" />
            )}
            <Star className="h-4 w-4 mr-2" />
            Starred
          </button>
          
          {expandedSections.includes('starred') && (
            <div className="ml-6 space-y-1">
              <div className="px-2 py-2 text-sm text-gray-400">
                No starred documents
              </div>
            </div>
          )}
        </div>
      </nav>
    </div>
  );
};

export default Sidebar;