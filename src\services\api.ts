import { 
  Course, 
  Module, 
  Lesson, 
  User, 
  UserProgress, 
  CourseEnrollment, 
  QuizSubmission, 
  AssignmentSubmission,
  CourseFilters,
  ApiResponse,
  PaginatedResponse,
  UserRole,
  CourseDifficulty,
  LessonType,
  ProgressStatus,
  EnrollmentStatus
} from '../types';

// Mock data storage
let mockCourses: Course[] = [];
let mockUsers: User[] = [];
let mockEnrollments: CourseEnrollment[] = [];
let mockProgress: UserProgress[] = [];
let mockSubmissions: (QuizSubmission | AssignmentSubmission)[] = [];

// Initialize mock data
const initializeMockData = () => {
  // Mock users
  mockUsers = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: UserRole.STUDENT,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=student',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      isActive: true,
    },
    {
      id: '2',
      name: '<PERSON>ru<PERSON>',
      email: '<EMAIL>',
      role: UserRole.INSTRUCTOR,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=instructor',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      isActive: true,
    },
    {
      id: '3',
      name: 'Admin User',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      isActive: true,
    },
  ];

  // Mock courses with modules and lessons
  mockCourses = [
    {
      id: '1',
      title: 'Introduction to React',
      description: 'Learn the fundamentals of React development from scratch. This comprehensive course covers components, state management, hooks, and modern React patterns.',
      thumbnail: 'https://via.placeholder.com/400x250/61DAFB/000000?text=React+Course',
      instructorId: '2',
      instructor: mockUsers[1],
      modules: [
        {
          id: 'module1',
          courseId: '1',
          title: 'Getting Started with React',
          description: 'Introduction to React concepts and setup',
          order: 1,
          lessons: [
            {
              id: 'lesson1',
              moduleId: 'module1',
              title: 'What is React?',
              description: 'Understanding React and its ecosystem',
              content: {
                type: LessonType.TEXT,
                data: {
                  html: '<h1>What is React?</h1><p>React is a JavaScript library for building user interfaces...</p>',
                  attachments: [],
                },
              },
              order: 1,
              type: LessonType.TEXT,
              estimatedDuration: 30,
              prerequisites: [],
              isPublished: true,
              createdAt: new Date('2024-01-15'),
              updatedAt: new Date(),
            },
            {
              id: 'lesson2',
              moduleId: 'module1',
              title: 'Setting up React Environment',
              description: 'Learn how to set up a React development environment',
              content: {
                type: LessonType.VIDEO,
                data: {
                  videoUrl: 'https://example.com/react-setup.mp4',
                  transcript: 'In this video, we will set up a React development environment...',
                  attachments: [],
                },
              },
              order: 2,
              type: LessonType.VIDEO,
              estimatedDuration: 45,
              prerequisites: ['lesson1'],
              isPublished: true,
              createdAt: new Date('2024-01-16'),
              updatedAt: new Date(),
            },
          ],
          prerequisites: [],
          estimatedDuration: 75,
          isPublished: true,
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date(),
        },
        {
          id: 'module2',
          courseId: '1',
          title: 'React Components',
          description: 'Deep dive into React components',
          order: 2,
          lessons: [
            {
              id: 'lesson3',
              moduleId: 'module2',
              title: 'Functional Components',
              description: 'Learn about functional components in React',
              content: {
                type: LessonType.TEXT,
                data: {
                  html: '<h1>Functional Components</h1><p>Functional components are the modern way to write React components...</p>',
                  attachments: [],
                },
              },
              order: 1,
              type: LessonType.TEXT,
              estimatedDuration: 40,
              prerequisites: ['lesson2'],
              isPublished: true,
              createdAt: new Date('2024-01-20'),
              updatedAt: new Date(),
            },
          ],
          prerequisites: ['module1'],
          estimatedDuration: 40,
          isPublished: true,
          createdAt: new Date('2024-01-20'),
          updatedAt: new Date(),
        },
      ],
      tags: ['React', 'JavaScript', 'Frontend', 'Web Development'],
      difficulty: CourseDifficulty.BEGINNER,
      estimatedDuration: 480, // 8 hours
      isPublished: true,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date(),
      prerequisites: [],
    },
    {
      id: '2',
      title: 'Advanced TypeScript',
      description: 'Master advanced TypeScript concepts including generics, decorators, and advanced type manipulation.',
      thumbnail: 'https://via.placeholder.com/400x250/3178C6/FFFFFF?text=TypeScript+Course',
      instructorId: '2',
      instructor: mockUsers[1],
      modules: [
        {
          id: 'module3',
          courseId: '2',
          title: 'Advanced Types',
          description: 'Learn about advanced TypeScript types',
          order: 1,
          lessons: [
            {
              id: 'lesson4',
              moduleId: 'module3',
              title: 'Generics in TypeScript',
              description: 'Understanding and using generics',
              content: {
                type: LessonType.TEXT,
                data: {
                  html: '<h1>Generics in TypeScript</h1><p>Generics provide a way to make components work with any data type...</p>',
                  attachments: [],
                },
              },
              order: 1,
              type: LessonType.TEXT,
              estimatedDuration: 60,
              prerequisites: [],
              isPublished: true,
              createdAt: new Date('2024-02-01'),
              updatedAt: new Date(),
            },
          ],
          prerequisites: [],
          estimatedDuration: 60,
          isPublished: true,
          createdAt: new Date('2024-02-01'),
          updatedAt: new Date(),
        },
      ],
      tags: ['TypeScript', 'JavaScript', 'Advanced', 'Programming'],
      difficulty: CourseDifficulty.ADVANCED,
      estimatedDuration: 720, // 12 hours
      isPublished: true,
      createdAt: new Date('2024-02-01'),
      updatedAt: new Date(),
      prerequisites: ['1'],
    },
  ];
};

// Initialize data on module load
initializeMockData();

// Utility function to simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Course API
export const courseApi = {
  async getCourses(filters?: CourseFilters): Promise<ApiResponse<Course[]>> {
    await delay(500);
    
    let filteredCourses = [...mockCourses];
    
    if (filters) {
      if (filters.difficulty) {
        filteredCourses = filteredCourses.filter(course => course.difficulty === filters.difficulty);
      }
      if (filters.tags && filters.tags.length > 0) {
        filteredCourses = filteredCourses.filter(course =>
          course.tags.some(tag => filters.tags!.includes(tag))
        );
      }
      if (filters.instructorId) {
        filteredCourses = filteredCourses.filter(course => course.instructorId === filters.instructorId);
      }
      if (filters.isPublished !== undefined) {
        filteredCourses = filteredCourses.filter(course => course.isPublished === filters.isPublished);
      }
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredCourses = filteredCourses.filter(course =>
          course.title.toLowerCase().includes(searchLower) ||
          course.description.toLowerCase().includes(searchLower) ||
          course.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }
    }
    
    return {
      data: filteredCourses,
      message: 'Courses retrieved successfully',
      success: true,
    };
  },

  async getCourse(courseId: string): Promise<ApiResponse<Course | null>> {
    await delay(300);
    
    const course = mockCourses.find(c => c.id === courseId);
    
    return {
      data: course || null,
      message: course ? 'Course retrieved successfully' : 'Course not found',
      success: !!course,
    };
  },

  async createCourse(courseData: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Course>> {
    await delay(800);
    
    const newCourse: Course = {
      ...courseData,
      id: `course-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    mockCourses.push(newCourse);
    
    return {
      data: newCourse,
      message: 'Course created successfully',
      success: true,
    };
  },

  async updateCourse(courseId: string, updates: Partial<Course>): Promise<ApiResponse<Course | null>> {
    await delay(500);
    
    const courseIndex = mockCourses.findIndex(c => c.id === courseId);
    
    if (courseIndex === -1) {
      return {
        data: null,
        message: 'Course not found',
        success: false,
      };
    }
    
    mockCourses[courseIndex] = {
      ...mockCourses[courseIndex],
      ...updates,
      updatedAt: new Date(),
    };
    
    return {
      data: mockCourses[courseIndex],
      message: 'Course updated successfully',
      success: true,
    };
  },

  async deleteCourse(courseId: string): Promise<ApiResponse<boolean>> {
    await delay(400);
    
    const courseIndex = mockCourses.findIndex(c => c.id === courseId);
    
    if (courseIndex === -1) {
      return {
        data: false,
        message: 'Course not found',
        success: false,
      };
    }
    
    mockCourses.splice(courseIndex, 1);
    
    return {
      data: true,
      message: 'Course deleted successfully',
      success: true,
    };
  },
};

// Enrollment API
export const enrollmentApi = {
  async getUserEnrollments(userId: string): Promise<ApiResponse<CourseEnrollment[]>> {
    await delay(300);
    
    const userEnrollments = mockEnrollments.filter(e => e.userId === userId);
    
    return {
      data: userEnrollments,
      message: 'Enrollments retrieved successfully',
      success: true,
    };
  },

  async enrollUser(userId: string, courseId: string): Promise<ApiResponse<CourseEnrollment>> {
    await delay(500);
    
    // Check if already enrolled
    const existingEnrollment = mockEnrollments.find(e => e.userId === userId && e.courseId === courseId);
    
    if (existingEnrollment) {
      return {
        data: existingEnrollment,
        message: 'User already enrolled in this course',
        success: false,
      };
    }
    
    const enrollment: CourseEnrollment = {
      id: `enrollment-${Date.now()}`,
      userId,
      courseId,
      enrolledAt: new Date(),
      progress: 0,
      status: EnrollmentStatus.ACTIVE,
    };
    
    mockEnrollments.push(enrollment);
    
    return {
      data: enrollment,
      message: 'Successfully enrolled in course',
      success: true,
    };
  },

  async unenrollUser(userId: string, courseId: string): Promise<ApiResponse<boolean>> {
    await delay(400);
    
    const enrollmentIndex = mockEnrollments.findIndex(e => e.userId === userId && e.courseId === courseId);
    
    if (enrollmentIndex === -1) {
      return {
        data: false,
        message: 'Enrollment not found',
        success: false,
      };
    }
    
    mockEnrollments.splice(enrollmentIndex, 1);
    
    return {
      data: true,
      message: 'Successfully unenrolled from course',
      success: true,
    };
  },
};

// Progress API
export const progressApi = {
  async getUserProgress(userId: string, courseId?: string): Promise<ApiResponse<UserProgress[]>> {
    await delay(300);
    
    let userProgress = mockProgress.filter(p => p.userId === userId);
    
    if (courseId) {
      userProgress = userProgress.filter(p => p.courseId === courseId);
    }
    
    return {
      data: userProgress,
      message: 'Progress retrieved successfully',
      success: true,
    };
  },

  async updateProgress(progressData: Partial<UserProgress> & { userId: string; lessonId: string }): Promise<ApiResponse<UserProgress>> {
    await delay(300);
    
    const existingProgressIndex = mockProgress.findIndex(
      p => p.userId === progressData.userId && p.lessonId === progressData.lessonId
    );
    
    if (existingProgressIndex !== -1) {
      mockProgress[existingProgressIndex] = {
        ...mockProgress[existingProgressIndex],
        ...progressData,
        updatedAt: new Date(),
      };
      
      return {
        data: mockProgress[existingProgressIndex],
        message: 'Progress updated successfully',
        success: true,
      };
    } else {
      const newProgress: UserProgress = {
        id: `progress-${Date.now()}`,
        userId: progressData.userId,
        courseId: progressData.courseId || '',
        lessonId: progressData.lessonId,
        status: progressData.status || ProgressStatus.IN_PROGRESS,
        timeSpent: progressData.timeSpent || 0,
        lastAccessedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        ...progressData,
      };
      
      mockProgress.push(newProgress);
      
      return {
        data: newProgress,
        message: 'Progress created successfully',
        success: true,
      };
    }
  },
};
