import React, { useState, useRef } from 'react';
import { Upload, FileText, Download, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { AssignmentContent as AssignmentContentType } from '../../types';

interface AssignmentContentProps {
  content: AssignmentContentType;
  onComplete: () => void;
  isCompleted: boolean;
}

const AssignmentContent: React.FC<AssignmentContentProps> = ({
  content,
  onComplete,
  isCompleted,
}) => {
  const [submission, setSubmission] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      
      // Check file type if restrictions exist
      if (content.allowedFileTypes && content.allowedFileTypes.length > 0) {
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        if (!fileExtension || !content.allowedFileTypes.includes(fileExtension)) {
          alert(`File ${file.name} has an unsupported file type.`);
          return false;
        }
      }
      
      return true;
    });

    setUploadedFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (!submission.trim() && uploadedFiles.length === 0) {
      alert('Please provide a text submission or upload files.');
      return;
    }

    setIsSubmitting(true);
    
    // Simulate submission process
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setHasSubmitted(true);
    setIsSubmitting(false);
    onComplete();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isOverdue = content.dueDate && new Date() > content.dueDate;
  const timeUntilDue = content.dueDate ? content.dueDate.getTime() - new Date().getTime() : null;
  const daysUntilDue = timeUntilDue ? Math.ceil(timeUntilDue / (1000 * 60 * 60 * 24)) : null;

  if (isCompleted || hasSubmitted) {
    return (
      <div className="p-6 text-center">
        <div className="flex items-center justify-center space-x-2 text-green-600 mb-4">
          <CheckCircle className="w-6 h-6" />
          <span className="text-lg font-medium">Assignment Submitted!</span>
        </div>
        <p className="text-gray-600">
          Your assignment has been submitted successfully. You will receive feedback once it's graded.
        </p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Assignment Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Assignment Instructions</h3>
            {content.dueDate && (
              <div className={`flex items-center space-x-2 text-sm ${
                isOverdue ? 'text-red-600' : daysUntilDue && daysUntilDue <= 3 ? 'text-yellow-600' : 'text-gray-600'
              }`}>
                <Clock className="w-4 h-4" />
                <span>
                  Due: {formatDate(content.dueDate)}
                  {isOverdue && ' (Overdue)'}
                  {!isOverdue && daysUntilDue && daysUntilDue <= 7 && ` (${daysUntilDue} day${daysUntilDue !== 1 ? 's' : ''} remaining)`}
                </span>
              </div>
            )}
          </div>
          
          {isOverdue && (
            <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-3 py-1 rounded-full">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm font-medium">Overdue</span>
            </div>
          )}
        </div>

        <div className="prose prose-lg max-w-none">
          <div dangerouslySetInnerHTML={{ __html: content.instructions }} />
        </div>

        {/* Assignment Requirements */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="bg-gray-50 p-3 rounded-lg">
            <span className="font-medium text-gray-700">Max Points:</span>
            <p className="text-gray-600">{content.maxPoints}</p>
          </div>
          {content.allowedFileTypes && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <span className="font-medium text-gray-700">Allowed Files:</span>
              <p className="text-gray-600">{content.allowedFileTypes.join(', ')}</p>
            </div>
          )}
          <div className="bg-gray-50 p-3 rounded-lg">
            <span className="font-medium text-gray-700">Submission Type:</span>
            <p className="text-gray-600">
              {content.submissionType === 'text' ? 'Text Entry' : 
               content.submissionType === 'file' ? 'File Upload' : 'Text & Files'}
            </p>
          </div>
        </div>
      </div>

      {/* Resources */}
      {content.resources && content.resources.length > 0 && (
        <div>
          <h4 className="font-semibold text-gray-900 mb-4">Assignment Resources</h4>
          <div className="space-y-3">
            {content.resources.map((resource) => (
              <div
                key={resource.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{resource.name}</p>
                    <p className="text-sm text-gray-600">
                      {resource.type} • {formatFileSize(resource.size)}
                    </p>
                  </div>
                </div>
                <a
                  href={resource.url}
                  download={resource.name}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </a>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Submission Form */}
      <div>
        <h4 className="font-semibold text-gray-900 mb-4">Your Submission</h4>
        
        {/* Text Submission */}
        {(content.submissionType === 'text' || content.submissionType === 'both') && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Text Submission
            </label>
            <textarea
              value={submission}
              onChange={(e) => setSubmission(e.target.value)}
              placeholder="Enter your assignment submission here..."
              rows={8}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
            />
          </div>
        )}

        {/* File Upload */}
        {(content.submissionType === 'file' || content.submissionType === 'both') && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              File Upload
            </label>
            
            <div
              onClick={() => fileInputRef.current?.click()}
              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
            >
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Click to upload files or drag and drop</p>
              <p className="text-sm text-gray-500 mt-1">
                {content.allowedFileTypes ? 
                  `Supported formats: ${content.allowedFileTypes.join(', ')}` : 
                  'All file types supported'
                } (Max 10MB per file)
              </p>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileUpload}
              accept={content.allowedFileTypes ? content.allowedFileTypes.map(type => `.${type}`).join(',') : undefined}
              className="hidden"
            />

            {/* Uploaded Files */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 space-y-2">
                <p className="text-sm font-medium text-gray-700">Uploaded Files:</p>
                {uploadedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="w-4 h-4 text-gray-600" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{file.name}</p>
                        <p className="text-xs text-gray-600">{formatFileSize(file.size)}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || (!submission.trim() && uploadedFiles.length === 0)}
            className={`px-6 py-3 rounded-md font-medium transition-colors ${
              isSubmitting || (!submission.trim() && uploadedFiles.length === 0)
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Assignment'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssignmentContent;
