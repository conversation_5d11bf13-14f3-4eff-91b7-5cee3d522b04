import React, { useState, useEffect } from 'react';
import { Download, FileText, CheckCircle } from 'lucide-react';
import { TextContent as TextContentType } from '../../types';

interface TextContentProps {
  content: TextContentType;
  onComplete: () => void;
  isCompleted: boolean;
}

const TextContent: React.FC<TextContentProps> = ({
  content,
  onComplete,
  isCompleted,
}) => {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [readingTime, setReadingTime] = useState(0);

  useEffect(() => {
    // Calculate estimated reading time (average 200 words per minute)
    const text = content.html.replace(/<[^>]*>/g, ''); // Strip HTML tags
    const wordCount = text.split(/\s+/).length;
    const estimatedTime = Math.ceil(wordCount / 200);
    setReadingTime(estimatedTime);
  }, [content.html]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
    
    // Consider "scrolled to bottom" when user has scrolled 90% of the content
    if (scrollPercentage >= 0.9 && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const handleMarkComplete = () => {
    if (!isCompleted) {
      onComplete();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="p-6">
      {/* Reading Time Indicator */}
      <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <FileText className="w-4 h-4" />
          <span>Estimated reading time: {readingTime} min{readingTime !== 1 ? 's' : ''}</span>
        </div>
        
        {isCompleted && (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Completed</span>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div 
        className="prose prose-lg max-w-none mb-8"
        onScroll={handleScroll}
        style={{ maxHeight: '70vh', overflowY: 'auto' }}
      >
        <div 
          dangerouslySetInnerHTML={{ __html: content.html }}
          className="leading-relaxed"
        />
      </div>

      {/* Attachments */}
      {content.attachments && content.attachments.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Attachments</h3>
          <div className="space-y-3">
            {content.attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{attachment.name}</p>
                    <p className="text-sm text-gray-600">
                      {attachment.type} • {formatFileSize(attachment.size)}
                    </p>
                  </div>
                </div>
                <a
                  href={attachment.url}
                  download={attachment.name}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </a>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Completion Section */}
      <div className="border-t border-gray-200 pt-6">
        {!isCompleted ? (
          <div className="text-center">
            {!hasScrolledToBottom ? (
              <div className="mb-4">
                <p className="text-gray-600 mb-2">
                  Please read through the entire lesson content to continue.
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: hasScrolledToBottom ? '100%' : '0%' }}
                  ></div>
                </div>
              </div>
            ) : (
              <div className="mb-4">
                <div className="flex items-center justify-center space-x-2 text-green-600 mb-2">
                  <CheckCircle className="w-5 h-5" />
                  <span>You've completed reading this lesson!</span>
                </div>
              </div>
            )}
            
            <button
              onClick={handleMarkComplete}
              disabled={!hasScrolledToBottom}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                hasScrolledToBottom
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              Mark as Complete
            </button>
          </div>
        ) : (
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 text-green-600 mb-4">
              <CheckCircle className="w-6 h-6" />
              <span className="text-lg font-medium">Lesson Completed!</span>
            </div>
            <p className="text-gray-600">
              Great job! You can now proceed to the next lesson.
            </p>
          </div>
        )}
      </div>

      {/* Reading Progress Indicator */}
      {!isCompleted && (
        <div className="fixed bottom-4 right-4 bg-white rounded-full shadow-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              hasScrolledToBottom ? 'bg-green-500' : 'bg-blue-500'
            }`}></div>
            <span className="text-sm font-medium text-gray-700">
              {hasScrolledToBottom ? 'Ready to complete' : 'Keep reading'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextContent;
