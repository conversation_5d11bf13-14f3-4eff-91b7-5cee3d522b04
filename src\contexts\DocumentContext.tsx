import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Document, DocumentState, User, CollaboratorCursor } from '../types';

interface DocumentContextType {
  state: DocumentState;
  createDocument: (title: string, type: Document['type']) => Promise<void>;
  updateDocument: (id: string, updates: Partial<Document>) => Promise<void>;
  deleteDocument: (id: string) => Promise<void>;
  setActiveDocument: (document: Document | null) => void;
  addCollaborator: (documentId: string, userId: string) => Promise<void>;
  removeCollaborator: (documentId: string, userId: string) => Promise<void>;
  updateCursor: (cursor: CollaboratorCursor) => void;
  loadDocuments: () => Promise<void>;
}

const DocumentContext = createContext<DocumentContextType | undefined>(undefined);

type DocumentAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'SET_DOCUMENTS'; payload: Document[] }
  | { type: 'ADD_DOCUMENT'; payload: Document }
  | { type: 'UPDATE_DOCUMENT'; payload: { id: string; updates: Partial<Document> } }
  | { type: 'DELETE_DOCUMENT'; payload: string }
  | { type: 'SET_ACTIVE_DOCUMENT'; payload: Document | null }
  | { type: 'SET_COLLABORATORS'; payload: User[] }
  | { type: 'UPDATE_CURSOR'; payload: CollaboratorCursor }
  | { type: 'REMOVE_CURSOR'; payload: string };

const documentReducer = (state: DocumentState, action: DocumentAction): DocumentState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_DOCUMENTS':
      return { ...state, documents: action.payload };
    case 'ADD_DOCUMENT':
      return { ...state, documents: [...state.documents, action.payload] };
    case 'UPDATE_DOCUMENT':
      return {
        ...state,
        documents: state.documents.map(doc =>
          doc.id === action.payload.id ? { ...doc, ...action.payload.updates } : doc
        ),
        activeDocument: state.activeDocument?.id === action.payload.id
          ? { ...state.activeDocument, ...action.payload.updates }
          : state.activeDocument,
      };
    case 'DELETE_DOCUMENT':
      return {
        ...state,
        documents: state.documents.filter(doc => doc.id !== action.payload),
        activeDocument: state.activeDocument?.id === action.payload ? null : state.activeDocument,
      };
    case 'SET_ACTIVE_DOCUMENT':
      return { ...state, activeDocument: action.payload };
    case 'SET_COLLABORATORS':
      return { ...state, collaborators: action.payload };
    case 'UPDATE_CURSOR':
      return {
        ...state,
        cursors: state.cursors.some(c => c.userId === action.payload.userId)
          ? state.cursors.map(c => c.userId === action.payload.userId ? action.payload : c)
          : [...state.cursors, action.payload],
      };
    case 'REMOVE_CURSOR':
      return {
        ...state,
        cursors: state.cursors.filter(c => c.userId !== action.payload),
      };
    default:
      return state;
  }
};

const initialState: DocumentState = {
  documents: [],
  activeDocument: null,
  collaborators: [],
  cursors: [],
  isLoading: false,
  error: null,
};

export const DocumentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(documentReducer, initialState);

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async (): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock documents
      const mockDocuments: Document[] = [
        {
          id: '1',
          title: 'Product Launch Strategy',
          content: 'This document outlines our comprehensive strategy for launching our new product...',
          type: 'report',
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-20'),
          createdBy: '1',
          collaborators: ['1', '2'],
          isPublic: false,
          version: 3,
          tags: ['strategy', 'product', 'launch'],
        },
        {
          id: '2',
          title: 'Team Meeting Notes',
          content: 'Meeting notes from our weekly team sync...',
          type: 'article',
          createdAt: new Date('2024-01-18'),
          updatedAt: new Date('2024-01-18'),
          createdBy: '1',
          collaborators: ['1'],
          isPublic: true,
          version: 1,
          tags: ['meeting', 'notes'],
        },
      ];
      
      dispatch({ type: 'SET_DOCUMENTS', payload: mockDocuments });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load documents' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const createDocument = async (title: string, type: Document['type']): Promise<void> => {
    const newDocument: Document = {
      id: Date.now().toString(),
      title,
      content: '',
      type,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: '1',
      collaborators: ['1'],
      isPublic: false,
      version: 1,
      tags: [],
    };
    
    dispatch({ type: 'ADD_DOCUMENT', payload: newDocument });
  };

  const updateDocument = async (id: string, updates: Partial<Document>): Promise<void> => {
    dispatch({ type: 'UPDATE_DOCUMENT', payload: { id, updates } });
  };

  const deleteDocument = async (id: string): Promise<void> => {
    dispatch({ type: 'DELETE_DOCUMENT', payload: id });
  };

  const setActiveDocument = (document: Document | null): void => {
    dispatch({ type: 'SET_ACTIVE_DOCUMENT', payload: document });
  };

  const addCollaborator = async (documentId: string, userId: string): Promise<void> => {
    const document = state.documents.find(doc => doc.id === documentId);
    if (document && !document.collaborators.includes(userId)) {
      await updateDocument(documentId, {
        collaborators: [...document.collaborators, userId],
      });
    }
  };

  const removeCollaborator = async (documentId: string, userId: string): Promise<void> => {
    const document = state.documents.find(doc => doc.id === documentId);
    if (document) {
      await updateDocument(documentId, {
        collaborators: document.collaborators.filter(id => id !== userId),
      });
    }
  };

  const updateCursor = (cursor: CollaboratorCursor): void => {
    dispatch({ type: 'UPDATE_CURSOR', payload: cursor });
  };

  return (
    <DocumentContext.Provider value={{
      state,
      createDocument,
      updateDocument,
      deleteDocument,
      setActiveDocument,
      addCollaborator,
      removeCollaborator,
      updateCursor,
      loadDocuments,
    }}>
      {children}
    </DocumentContext.Provider>
  );
};

export const useDocument = (): DocumentContextType => {
  const context = useContext(DocumentContext);
  if (!context) {
    throw new Error('useDocument must be used within a DocumentProvider');
  }
  return context;
};