import React from 'react';
import { Clock, User, BookOpen, Star, Lock } from 'lucide-react';
import { Course, CourseEnrollment, CourseDifficulty } from '../../types';
import { useAuth } from '../../contexts/AuthContext';

interface CourseCardProps {
  course: Course;
  enrollment?: CourseEnrollment;
  onEnroll?: (courseId: string) => void;
  onView?: (courseId: string) => void;
  showEnrollButton?: boolean;
}

const CourseCard: React.FC<CourseCardProps> = ({
  course,
  enrollment,
  onEnroll,
  onView,
  showEnrollButton = true,
}) => {
  const { state: authState } = useAuth();

  const getDifficultyColor = (difficulty: CourseDifficulty) => {
    switch (difficulty) {
      case CourseDifficulty.BEGINNER:
        return 'bg-green-100 text-green-800';
      case CourseDifficulty.INTERMEDIATE:
        return 'bg-yellow-100 text-yellow-800';
      case CourseDifficulty.ADVANCED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const isEnrolled = !!enrollment;
  const canEnroll = !course.prerequisites.length || 
    course.prerequisites.every(prereqId => {
      // This would check if user has completed prerequisite courses
      // For now, we'll assume they can enroll
      return true;
    });

  const handleCardClick = () => {
    if (onView) {
      onView(course.id);
    }
  };

  const handleEnrollClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEnroll && !isEnrolled) {
      onEnroll(course.id);
    }
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer overflow-hidden"
      onClick={handleCardClick}
    >
      {/* Course Thumbnail */}
      <div className="relative">
        <img
          src={course.thumbnail || 'https://via.placeholder.com/400x250/f3f4f6/9ca3af?text=Course+Image'}
          alt={course.title}
          className="w-full h-48 object-cover"
        />
        {!canEnroll && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="text-white text-center">
              <Lock className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">Prerequisites Required</p>
            </div>
          </div>
        )}
        {isEnrolled && (
          <div className="absolute top-2 right-2">
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Enrolled
            </span>
          </div>
        )}
      </div>

      {/* Course Content */}
      <div className="p-6">
        {/* Course Title */}
        <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
          {course.title}
        </h3>

        {/* Course Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {course.description}
        </p>

        {/* Course Meta Information */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <User className="w-4 h-4 mr-1" />
              <span>{course.instructor.name}</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              <span>{formatDuration(course.estimatedDuration)}</span>
            </div>
            <div className="flex items-center">
              <BookOpen className="w-4 h-4 mr-1" />
              <span>{course.modules.length} modules</span>
            </div>
          </div>
        </div>

        {/* Difficulty Badge */}
        <div className="flex items-center justify-between mb-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(course.difficulty)}`}>
            {course.difficulty.charAt(0).toUpperCase() + course.difficulty.slice(1)}
          </span>
          
          {enrollment && (
            <div className="flex items-center">
              <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${enrollment.progress}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-600">{enrollment.progress}%</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {course.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {course.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
              >
                {tag}
              </span>
            ))}
            {course.tags.length > 3 && (
              <span className="text-gray-500 text-xs">
                +{course.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Prerequisites */}
        {course.prerequisites.length > 0 && (
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              <span className="font-medium">Prerequisites:</span> {course.prerequisites.length} course(s)
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2">
          {isEnrolled ? (
            <button
              onClick={handleCardClick}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
            >
              Continue Learning
            </button>
          ) : (
            <>
              <button
                onClick={handleCardClick}
                className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors duration-200 text-sm font-medium"
              >
                View Details
              </button>
              {showEnrollButton && canEnroll && authState.user && (
                <button
                  onClick={handleEnrollClick}
                  className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors duration-200 text-sm font-medium"
                >
                  Enroll Now
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourseCard;
