import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Clock, 
  Trophy, 
  TrendingUp, 
  Calendar,
  Target,
  Award,
  ChevronRight,
  Filter
} from 'lucide-react';
import { Course, CourseEnrollment, UserProgress, ProgressStatus } from '../../types';
import ProgressBar, { CircularProgress } from './ProgressBar';
import { useCourse } from '../../contexts/CourseContext';
import { useLesson } from '../../contexts/LessonContext';

interface ProgressDashboardProps {
  onCourseSelect?: (courseId: string) => void;
  className?: string;
}

const ProgressDashboard: React.FC<ProgressDashboardProps> = ({
  onCourseSelect,
  className = '',
}) => {
  const { state: courseState } = useCourse();
  const { state: lessonState } = useLesson();
  
  const [timeFilter, setTimeFilter] = useState<'week' | 'month' | 'all'>('week');
  const [sortBy, setSortBy] = useState<'progress' | 'recent' | 'name'>('progress');

  // Calculate overall statistics
  const enrolledCourses = courseState.enrollments;
  const totalCourses = enrolledCourses.length;
  const completedCourses = enrolledCourses.filter(e => e.progress >= 100).length;
  const inProgressCourses = enrolledCourses.filter(e => e.progress > 0 && e.progress < 100).length;
  
  // Calculate total time spent across all courses
  const totalTimeSpent = lessonState.progress.reduce((total, progress) => total + progress.timeSpent, 0);
  
  // Calculate recent activity
  const now = new Date();
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  
  const getFilterDate = () => {
    switch (timeFilter) {
      case 'week': return weekAgo;
      case 'month': return monthAgo;
      default: return new Date(0);
    }
  };

  const recentProgress = lessonState.progress.filter(p => 
    p.lastAccessed && p.lastAccessed >= getFilterDate()
  );

  // Get course details with progress
  const coursesWithProgress = enrolledCourses.map(enrollment => {
    const course = courseState.courses.find(c => c.id === enrollment.courseId);
    if (!course) return null;

    const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
    const completedLessons = lessonState.progress.filter(p => 
      course.modules.some(m => m.lessons.some(l => l.id === p.lessonId)) &&
      p.status === ProgressStatus.COMPLETED
    ).length;

    const courseTimeSpent = lessonState.progress
      .filter(p => course.modules.some(m => m.lessons.some(l => l.id === p.lessonId)))
      .reduce((total, p) => total + p.timeSpent, 0);

    return {
      course,
      enrollment,
      totalLessons,
      completedLessons,
      timeSpent: courseTimeSpent,
      lastActivity: Math.max(
        ...lessonState.progress
          .filter(p => course.modules.some(m => m.lessons.some(l => l.id === p.lessonId)))
          .map(p => p.lastAccessed?.getTime() || 0)
      )
    };
  }).filter(Boolean);

  // Sort courses
  const sortedCourses = [...coursesWithProgress].sort((a, b) => {
    switch (sortBy) {
      case 'progress':
        return b!.enrollment.progress - a!.enrollment.progress;
      case 'recent':
        return b!.lastActivity - a!.lastActivity;
      case 'name':
        return a!.course.title.localeCompare(b!.course.title);
      default:
        return 0;
    }
  });

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const getProgressColor = (percentage: number): 'blue' | 'green' | 'yellow' | 'red' => {
    if (percentage >= 100) return 'green';
    if (percentage >= 75) return 'blue';
    if (percentage >= 50) return 'yellow';
    return 'red';
  };

  const formatLastActivity = (timestamp: number): string => {
    if (timestamp === 0) return 'No activity';
    
    const date = new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{totalCourses}</p>
              <p className="text-sm text-gray-600">Enrolled Courses</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-lg">
              <Trophy className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{completedCourses}</p>
              <p className="text-sm text-gray-600">Completed</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{inProgressCourses}</p>
              <p className="text-sm text-gray-600">In Progress</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900">{formatTime(totalTimeSpent)}</p>
              <p className="text-sm text-gray-600">Total Time</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value as 'week' | 'month' | 'all')}
              className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="week">Past Week</option>
              <option value="month">Past Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>

        {recentProgress.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No recent activity in the selected time period.</p>
            <p className="text-sm mt-1">Start learning to see your progress here!</p>
          </div>
        ) : (
          <div className="space-y-3">
            {recentProgress.slice(0, 5).map((progress) => {
              const lesson = courseState.courses
                .flatMap(c => c.modules)
                .flatMap(m => m.lessons)
                .find(l => l.id === progress.lessonId);
              
              if (!lesson) return null;

              return (
                <div key={progress.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      progress.status === ProgressStatus.COMPLETED 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-blue-100 text-blue-600'
                    }`}>
                      {progress.status === ProgressStatus.COMPLETED ? (
                        <Trophy className="w-4 h-4" />
                      ) : (
                        <Target className="w-4 h-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{lesson.title}</p>
                      <p className="text-sm text-gray-600">
                        {progress.status === ProgressStatus.COMPLETED ? 'Completed' : 'In Progress'} • 
                        {formatTime(progress.timeSpent)} spent
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {progress.lastAccessed ? formatLastActivity(progress.lastAccessed.getTime()) : 'Unknown'}
                    </p>
                    {progress.score !== undefined && (
                      <p className="text-sm font-medium text-green-600">{progress.score}%</p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Course Progress */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Course Progress</h3>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'progress' | 'recent' | 'name')}
            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="progress">Sort by Progress</option>
            <option value="recent">Sort by Recent Activity</option>
            <option value="name">Sort by Name</option>
          </select>
        </div>

        {sortedCourses.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No enrolled courses found.</p>
            <p className="text-sm mt-1">Enroll in a course to start learning!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedCourses.map((courseData) => (
              <div
                key={courseData!.course.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onCourseSelect?.(courseData!.course.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{courseData!.course.title}</h4>
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <span>{courseData!.completedLessons}/{courseData!.totalLessons} lessons</span>
                      <span>{formatTime(courseData!.timeSpent)} spent</span>
                      <span>Last activity: {formatLastActivity(courseData!.lastActivity)}</span>
                    </div>

                    <ProgressBar
                      progress={courseData!.enrollment.progress}
                      color={getProgressColor(courseData!.enrollment.progress)}
                      size="sm"
                    />
                  </div>
                  
                  <div className="ml-6">
                    <CircularProgress
                      progress={courseData!.enrollment.progress}
                      size={60}
                      strokeWidth={6}
                      color={getProgressColor(courseData!.enrollment.progress)}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressDashboard;
