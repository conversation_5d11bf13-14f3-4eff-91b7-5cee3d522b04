import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Clock,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  BookOpen,
  Play,
  FileText,
  HelpCircle,
  Assignment,
  Home
} from 'lucide-react';
import { Lesson, LessonType, UserProgress, ProgressStatus } from '../../types';
import { useLesson } from '../../contexts/LessonContext';
import { useCourse } from '../../contexts/CourseContext';
import { useAuth } from '../../contexts/AuthContext';
import TextContent from './TextContent';
import VideoContent from './VideoContent';
import QuizContent from './QuizContent';
import AssignmentContent from './AssignmentContent';
import InteractiveContent from './InteractiveContent';

interface LessonViewerProps {
  className?: string;
}

const LessonViewer: React.FC<LessonViewerProps> = ({
  className = '',
}) => {
  const { lessonId } = useParams<{ lessonId: string }>();
  const navigate = useNavigate();
  const { state: lessonState, actions: lessonActions } = useLesson();
  const { state: courseState } = useCourse();
  const { user } = useAuth();

  const [startTime, setStartTime] = useState<Date>(new Date());
  const [isCompleted, setIsCompleted] = useState(false);
  const [currentProgress, setCurrentProgress] = useState<UserProgress | null>(null);
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [courseId, setCourseId] = useState<string | null>(null);

  useEffect(() => {
    if (lessonId) {
      lessonActions.loadLesson(lessonId);
      setStartTime(new Date());

      // Find the course that contains this lesson
      const foundCourse = courseState.courses.find(course =>
        course.modules.some(module =>
          module.lessons.some(l => l.id === lessonId)
        )
      );

      if (foundCourse) {
        setCourseId(foundCourse.id);
        const foundLesson = foundCourse.modules
          .flatMap(module => module.lessons)
          .find(l => l.id === lessonId);
        setLesson(foundLesson || null);
      }
    }
  }, [lessonId, courseState.courses]);

  useEffect(() => {
    if (lessonState.activeLesson) {
      const progress = lessonState.progress.find(p => p.lessonId === lessonId);
      setCurrentProgress(progress || null);
      setIsCompleted(progress?.status === ProgressStatus.COMPLETED);
    }
  }, [lessonState.activeLesson, lessonState.progress, lessonId]);

  // Track time spent
  useEffect(() => {
    const interval = setInterval(() => {
      if (lessonState.activeLesson && !isCompleted) {
        const timeSpent = Math.floor((new Date().getTime() - startTime.getTime()) / 1000 / 60); // in minutes
        if (timeSpent > 0) {
          updateProgress(lessonId, 1); // Update every minute
        }
      }
    }, 60000); // Every minute

    return () => clearInterval(interval);
  }, [lessonId, startTime, isCompleted, lessonState.activeLesson]);

  const handleComplete = async (score?: number) => {
    if (!lessonState.activeLesson || !user) return;

    const timeSpent = Math.floor((new Date().getTime() - startTime.getTime()) / 1000 / 60);
    lessonActions.updateProgress(lessonId!, timeSpent);

    await lessonActions.completeLesson(lessonId!, user.id, score, timeSpent);
    setIsCompleted(true);
  };

  const handleBackToCourse = () => {
    if (courseId) {
      navigate(`/courses/${courseId}`);
    } else {
      navigate('/courses');
    }
  };

  const getNextLesson = (): string | null => {
    if (!lesson || !courseId) return null;

    const course = courseState.courses.find(c => c.id === courseId);
    if (!course) return null;

    const allLessons = course.modules.flatMap(module => module.lessons);
    const currentIndex = allLessons.findIndex(l => l.id === lessonId);

    if (currentIndex >= 0 && currentIndex < allLessons.length - 1) {
      return allLessons[currentIndex + 1].id;
    }

    return null;
  };

  const getPreviousLesson = (): string | null => {
    if (!lesson || !courseId) return null;

    const course = courseState.courses.find(c => c.id === courseId);
    if (!course) return null;

    const allLessons = course.modules.flatMap(module => module.lessons);
    const currentIndex = allLessons.findIndex(l => l.id === lessonId);

    if (currentIndex > 0) {
      return allLessons[currentIndex - 1].id;
    }

    return null;
  };

  const handleNext = () => {
    const nextLessonId = getNextLesson();
    if (nextLessonId) {
      navigate(`/lessons/${nextLessonId}`);
    }
  };

  const handlePrevious = () => {
    const previousLessonId = getPreviousLesson();
    if (previousLessonId) {
      navigate(`/lessons/${previousLessonId}`);
    }
  };

  const getLessonIcon = (type: LessonType) => {
    switch (type) {
      case LessonType.VIDEO:
        return <Play className="w-5 h-5" />;
      case LessonType.TEXT:
        return <FileText className="w-5 h-5" />;
      case LessonType.QUIZ:
        return <HelpCircle className="w-5 h-5" />;
      case LessonType.ASSIGNMENT:
        return <Assignment className="w-5 h-5" />;
      case LessonType.INTERACTIVE:
        return <BookOpen className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const renderLessonContent = () => {
    if (!lessonState.activeLesson) return null;

    const { content } = lessonState.activeLesson;

    switch (content.type) {
      case LessonType.TEXT:
        return (
          <TextContent 
            content={content.data as any}
            onComplete={() => handleComplete()}
            isCompleted={isCompleted}
          />
        );
      case LessonType.VIDEO:
        return (
          <VideoContent 
            content={content.data as any}
            onComplete={() => handleComplete()}
            isCompleted={isCompleted}
          />
        );
      case LessonType.QUIZ:
        return (
          <QuizContent 
            content={content.data as any}
            onComplete={handleComplete}
            isCompleted={isCompleted}
          />
        );
      case LessonType.ASSIGNMENT:
        return (
          <AssignmentContent 
            content={content.data as any}
            onComplete={() => handleComplete()}
            isCompleted={isCompleted}
          />
        );
      case LessonType.INTERACTIVE:
        return (
          <InteractiveContent 
            content={content.data as any}
            onComplete={() => handleComplete()}
            isCompleted={isCompleted}
          />
        );
      default:
        return <div className="text-center py-8 text-gray-500">Unsupported content type</div>;
    }
  };

  if (lessonState.isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading lesson...</span>
      </div>
    );
  }

  if (lessonState.error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{lessonState.error}</p>
        <button
          onClick={() => loadLesson(lessonId)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!lessonState.activeLesson) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Lesson not found</p>
      </div>
    );
  }

  const activeLesson = lessonState.activeLesson;

  if (lessonState.isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading lesson...</span>
      </div>
    );
  }

  if (!activeLesson) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Lesson not found</p>
      </div>
    );
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* Back Button */}
      <button
        onClick={handleBackToCourse}
        className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors mb-6"
      >
        <ArrowLeft className="w-4 h-4" />
        <span>Back to Course</span>
      </button>

      {/* Lesson Header */}
      <div className="bg-white rounded-lg shadow-md mb-6">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${
                isCompleted ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
              }`}>
                {isCompleted ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  getLessonIcon(activeLesson.type)
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{activeLesson.title}</h1>
                <p className="text-gray-600">{activeLesson.description}</p>
              </div>
            </div>
            
            {isCompleted && (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Completed</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              <span>Est. {formatDuration(activeLesson.estimatedDuration)}</span>
            </div>
            {currentProgress && (
              <div className="flex items-center">
                <span>Time spent: {formatDuration(currentProgress.timeSpent)}</span>
              </div>
            )}
            {currentProgress?.score !== undefined && (
              <div className="flex items-center">
                <span>Score: {currentProgress.score}%</span>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <div className="px-6 py-4 bg-gray-50 flex items-center justify-between">
          <button
            onClick={handlePrevious}
            disabled={!getPreviousLesson()}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              getPreviousLesson()
                ? 'text-blue-600 hover:bg-blue-50'
                : 'text-gray-400 cursor-not-allowed'
            }`}
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>

          <div className="text-sm text-gray-600">
            Lesson {activeLesson.order}
          </div>

          <button
            onClick={handleNext}
            disabled={!getNextLesson() || !isCompleted}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              getNextLesson() && isCompleted
                ? 'text-blue-600 hover:bg-blue-50'
                : 'text-gray-400 cursor-not-allowed'
            }`}
          >
            <span>Next</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Lesson Content */}
      <div className="bg-white rounded-lg shadow-md">
        {renderLessonContent()}
      </div>
    </div>
  );
};

export default LessonViewer;
