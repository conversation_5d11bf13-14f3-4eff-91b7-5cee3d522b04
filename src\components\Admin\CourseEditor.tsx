import React, { useState, useEffect } from 'react';
import {
  Save,
  Plus,
  Trash2,
  Edit,
  Move,
  Eye,
  Upload,
  X,
  ChevronDown,
  ChevronRight,
  BookOpen,
  Video,
  FileText,
  HelpCircle,
  Target,
  Settings
} from 'lucide-react';
import { Course, Module, Lesson, CourseDifficulty, LessonType } from '../../types';
import { useCourse } from '../../contexts/CourseContext';

interface CourseEditorProps {
  courseId?: string;
  onSave?: (course: Course) => void;
  onCancel?: () => void;
  className?: string;
}

const CourseEditor: React.FC<CourseEditorProps> = ({
  courseId,
  onSave,
  onCancel,
  className = '',
}) => {
  const { state: courseState, actions: courseActions } = useCourse();
  
  const [course, setCourse] = useState<Course>({
    id: '',
    title: '',
    description: '',
    difficulty: CourseDifficulty.BEGINNER,
    estimatedDuration: 0,
    tags: [],
    prerequisites: [],
    modules: [],
    instructorId: '',
    createdAt: new Date(),
    updatedAt: new Date(),
    isPublished: false,
    rating: 0,
    enrollmentCount: 0
  });

  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [editingModule, setEditingModule] = useState<string | null>(null);
  const [editingLesson, setEditingLesson] = useState<string | null>(null);
  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (courseId) {
      const existingCourse = courseState.courses.find(c => c.id === courseId);
      if (existingCourse) {
        setCourse(existingCourse);
        // Expand all modules by default when editing
        setExpandedModules(new Set(existingCourse.modules.map(m => m.id)));
      }
    }
  }, [courseId, courseState.courses]);

  const validateCourse = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!course.title.trim()) {
      newErrors.title = 'Course title is required';
    }

    if (!course.description.trim()) {
      newErrors.description = 'Course description is required';
    }

    if (course.modules.length === 0) {
      newErrors.modules = 'At least one module is required';
    }

    course.modules.forEach((module, moduleIndex) => {
      if (!module.title.trim()) {
        newErrors[`module-${moduleIndex}-title`] = 'Module title is required';
      }

      if (module.lessons.length === 0) {
        newErrors[`module-${moduleIndex}-lessons`] = 'At least one lesson is required per module';
      }

      module.lessons.forEach((lesson, lessonIndex) => {
        if (!lesson.title.trim()) {
          newErrors[`lesson-${moduleIndex}-${lessonIndex}-title`] = 'Lesson title is required';
        }
      });
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateCourse()) {
      return;
    }

    try {
      const updatedCourse = {
        ...course,
        id: course.id || `course-${Date.now()}`,
        updatedAt: new Date(),
        estimatedDuration: calculateTotalDuration()
      };

      if (courseId) {
        await courseActions.updateCourse(updatedCourse);
      } else {
        await courseActions.createCourse(updatedCourse);
      }

      onSave?.(updatedCourse);
    } catch (error) {
      console.error('Failed to save course:', error);
    }
  };

  const calculateTotalDuration = (): number => {
    return course.modules.reduce((total, module) => 
      total + module.lessons.reduce((moduleTotal, lesson) => 
        moduleTotal + (lesson.estimatedDuration || 0), 0
      ), 0
    );
  };

  const addModule = () => {
    const newModule: Module = {
      id: `module-${Date.now()}`,
      title: 'New Module',
      description: '',
      lessons: [],
      order: course.modules.length,
      estimatedDuration: 0,
      prerequisites: []
    };

    setCourse(prev => ({
      ...prev,
      modules: [...prev.modules, newModule]
    }));

    setExpandedModules(prev => new Set([...prev, newModule.id]));
    setEditingModule(newModule.id);
  };

  const updateModule = (moduleId: string, updates: Partial<Module>) => {
    setCourse(prev => ({
      ...prev,
      modules: prev.modules.map(module =>
        module.id === moduleId ? { ...module, ...updates } : module
      )
    }));
  };

  const deleteModule = (moduleId: string) => {
    setCourse(prev => ({
      ...prev,
      modules: prev.modules.filter(module => module.id !== moduleId)
    }));
  };

  const addLesson = (moduleId: string) => {
    const newLesson: Lesson = {
      id: `lesson-${Date.now()}`,
      title: 'New Lesson',
      type: LessonType.TEXT,
      content: {
        type: LessonType.TEXT,
        text: '',
        attachments: []
      },
      order: 0,
      estimatedDuration: 15,
      prerequisites: []
    };

    setCourse(prev => ({
      ...prev,
      modules: prev.modules.map(module =>
        module.id === moduleId
          ? {
              ...module,
              lessons: [...module.lessons, { ...newLesson, order: module.lessons.length }]
            }
          : module
      )
    }));

    setEditingLesson(newLesson.id);
  };

  const updateLesson = (moduleId: string, lessonId: string, updates: Partial<Lesson>) => {
    setCourse(prev => ({
      ...prev,
      modules: prev.modules.map(module =>
        module.id === moduleId
          ? {
              ...module,
              lessons: module.lessons.map(lesson =>
                lesson.id === lessonId ? { ...lesson, ...updates } : lesson
              )
            }
          : module
      )
    }));
  };

  const deleteLesson = (moduleId: string, lessonId: string) => {
    setCourse(prev => ({
      ...prev,
      modules: prev.modules.map(module =>
        module.id === moduleId
          ? {
              ...module,
              lessons: module.lessons.filter(lesson => lesson.id !== lessonId)
            }
          : module
      )
    }));
  };

  const toggleModule = (moduleId: string) => {
    setExpandedModules(prev => {
      const newSet = new Set(prev);
      if (newSet.has(moduleId)) {
        newSet.delete(moduleId);
      } else {
        newSet.add(moduleId);
      }
      return newSet;
    });
  };

  const addTag = () => {
    if (newTag.trim() && !course.tags.includes(newTag.trim())) {
      setCourse(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setCourse(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const getLessonIcon = (type: LessonType) => {
    switch (type) {
      case LessonType.VIDEO:
        return <Video className="w-4 h-4" />;
      case LessonType.QUIZ:
        return <HelpCircle className="w-4 h-4" />;
      case LessonType.ASSIGNMENT:
        return <Target className="w-4 h-4" />;
      case LessonType.INTERACTIVE:
        return <BookOpen className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {courseId ? 'Edit Course' : 'Create New Course'}
          </h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Save Course</span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6 max-h-96 overflow-y-auto">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Course Title</label>
            <input
              type="text"
              value={course.title}
              onChange={(e) => setCourse(prev => ({ ...prev, title: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.title ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter course title"
            />
            {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea
              value={course.description}
              onChange={(e) => setCourse(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter course description"
            />
            {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
              <select
                value={course.difficulty}
                onChange={(e) => setCourse(prev => ({ ...prev, difficulty: e.target.value as CourseDifficulty }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={CourseDifficulty.BEGINNER}>Beginner</option>
                <option value={CourseDifficulty.INTERMEDIATE}>Intermediate</option>
                <option value={CourseDifficulty.ADVANCED}>Advanced</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estimated Duration (minutes)
              </label>
              <input
                type="number"
                value={calculateTotalDuration()}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                placeholder="Auto-calculated"
              />
            </div>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {course.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                >
                  {tag}
                  <button
                    onClick={() => removeTag(tag)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
            <div className="flex space-x-2">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addTag()}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Add a tag"
              />
              <button
                onClick={addTag}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              >
                Add
              </button>
            </div>
          </div>

          {/* Published Status */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="published"
              checked={course.isPublished}
              onChange={(e) => setCourse(prev => ({ ...prev, isPublished: e.target.checked }))}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="published" className="text-sm font-medium text-gray-700">
              Publish course (make it available to students)
            </label>
          </div>
        </div>

        {/* Modules */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Course Modules</h3>
            <button
              onClick={addModule}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Module</span>
            </button>
          </div>

          {errors.modules && <p className="text-red-600 text-sm">{errors.modules}</p>}

          <div className="space-y-3">
            {course.modules.map((module, moduleIndex) => (
              <div key={module.id} className="border border-gray-200 rounded-lg">
                <div className="p-4 bg-gray-50 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      <button
                        onClick={() => toggleModule(module.id)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {expandedModules.has(module.id) ? (
                          <ChevronDown className="w-5 h-5" />
                        ) : (
                          <ChevronRight className="w-5 h-5" />
                        )}
                      </button>
                      
                      {editingModule === module.id ? (
                        <input
                          type="text"
                          value={module.title}
                          onChange={(e) => updateModule(module.id, { title: e.target.value })}
                          onBlur={() => setEditingModule(null)}
                          onKeyPress={(e) => e.key === 'Enter' && setEditingModule(null)}
                          className="flex-1 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          autoFocus
                        />
                      ) : (
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{module.title}</h4>
                          <p className="text-sm text-gray-600">{module.lessons.length} lessons</p>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setEditingModule(module.id)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => deleteModule(module.id)}
                        className="text-red-400 hover:text-red-600"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  {errors[`module-${moduleIndex}-title`] && (
                    <p className="text-red-600 text-sm mt-1">{errors[`module-${moduleIndex}-title`]}</p>
                  )}
                  {errors[`module-${moduleIndex}-lessons`] && (
                    <p className="text-red-600 text-sm mt-1">{errors[`module-${moduleIndex}-lessons`]}</p>
                  )}
                </div>

                {expandedModules.has(module.id) && (
                  <div className="p-4">
                    <div className="mb-4">
                      <textarea
                        value={module.description}
                        onChange={(e) => updateModule(module.id, { description: e.target.value })}
                        placeholder="Module description"
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-medium text-gray-900">Lessons</h5>
                      <button
                        onClick={() => addLesson(module.id)}
                        className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors text-sm"
                      >
                        <Plus className="w-3 h-3" />
                        <span>Add Lesson</span>
                      </button>
                    </div>

                    <div className="space-y-2">
                      {module.lessons.map((lesson, lessonIndex) => (
                        <div
                          key={lesson.id}
                          className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md"
                        >
                          <div className="flex items-center space-x-3 flex-1">
                            {getLessonIcon(lesson.type)}
                            
                            {editingLesson === lesson.id ? (
                              <div className="flex-1 space-y-2">
                                <input
                                  type="text"
                                  value={lesson.title}
                                  onChange={(e) => updateLesson(module.id, lesson.id, { title: e.target.value })}
                                  className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                                <div className="flex space-x-2">
                                  <select
                                    value={lesson.type}
                                    onChange={(e) => updateLesson(module.id, lesson.id, { type: e.target.value as LessonType })}
                                    className="px-2 py-1 border border-gray-300 rounded text-sm"
                                  >
                                    <option value={LessonType.TEXT}>Text</option>
                                    <option value={LessonType.VIDEO}>Video</option>
                                    <option value={LessonType.QUIZ}>Quiz</option>
                                    <option value={LessonType.ASSIGNMENT}>Assignment</option>
                                    <option value={LessonType.INTERACTIVE}>Interactive</option>
                                  </select>
                                  <input
                                    type="number"
                                    value={lesson.estimatedDuration || 15}
                                    onChange={(e) => updateLesson(module.id, lesson.id, { estimatedDuration: parseInt(e.target.value) })}
                                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                    placeholder="Duration"
                                  />
                                  <button
                                    onClick={() => setEditingLesson(null)}
                                    className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200"
                                  >
                                    Done
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">{lesson.title}</p>
                                <p className="text-sm text-gray-600">
                                  {lesson.type} • {lesson.estimatedDuration || 15} min
                                </p>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => setEditingLesson(lesson.id)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => deleteLesson(module.id, lesson.id)}
                              className="text-red-400 hover:text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseEditor;
