import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, AuthState, UserRole } from '../types';

interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string, role?: UserRole) => Promise<void>;
  logout: () => void;
  updateUser: (user: Partial<User>) => void;
  hasPermission: (permission: Permission) => boolean;
  isRole: (role: UserRole) => boolean;
}

export enum Permission {
  VIEW_COURSES = 'view_courses',
  ENROLL_COURSES = 'enroll_courses',
  CREATE_COURSES = 'create_courses',
  EDIT_COURSES = 'edit_courses',
  DELETE_COURSES = 'delete_courses',
  MANAGE_USERS = 'manage_users',
  VIEW_ANALYTICS = 'view_analytics',
  GRADE_ASSIGNMENTS = 'grade_assignments'
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true, error: null };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isLoading: false,
        user: action.payload.user,
        token: action.payload.token,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return { ...state, isLoading: false, error: action.payload };
    case 'LOGOUT':
      return { user: null, token: null, isLoading: false, error: null };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    default:
      return state;
  }
};

const initialState: AuthState = {
  user: null,
  token: null,
  isLoading: false,
  error: null,
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('userData');
    
    if (token && userData) {
      try {
        const user = JSON.parse(userData);
        dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token } });
      } catch (error) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      }
    }
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    dispatch({ type: 'LOGIN_START' });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data - determine role based on email domain or default to student
      let role = UserRole.STUDENT;
      if (email.includes('instructor') || email.includes('teacher')) {
        role = UserRole.INSTRUCTOR;
      } else if (email.includes('admin')) {
        role = UserRole.ADMIN;
      }

      const user: User = {
        id: '1',
        email,
        name: email.split('@')[0],
        role,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
      };
      
      const token = `mock-jwt-token-${Date.now()}`;
      
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(user));
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token } });
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE', payload: 'Invalid credentials' });
    }
  };

  const register = async (email: string, password: string, name: string, role: UserRole = UserRole.STUDENT): Promise<void> => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const user: User = {
        id: Date.now().toString(),
        email,
        name,
        role,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
      };

      const token = `mock-jwt-token-${Date.now()}`;

      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(user));

      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token } });
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE', payload: 'Registration failed' });
    }
  };

  const logout = (): void => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    dispatch({ type: 'LOGOUT' });
  };

  const updateUser = (userData: Partial<User>): void => {
    dispatch({ type: 'UPDATE_USER', payload: userData });
  };

  const hasPermission = (permission: Permission): boolean => {
    if (!state.user) return false;

    const rolePermissions: Record<UserRole, Permission[]> = {
      [UserRole.STUDENT]: [
        Permission.VIEW_COURSES,
        Permission.ENROLL_COURSES
      ],
      [UserRole.INSTRUCTOR]: [
        Permission.VIEW_COURSES,
        Permission.ENROLL_COURSES,
        Permission.CREATE_COURSES,
        Permission.EDIT_COURSES,
        Permission.GRADE_ASSIGNMENTS,
        Permission.VIEW_ANALYTICS
      ],
      [UserRole.ADMIN]: [
        Permission.VIEW_COURSES,
        Permission.ENROLL_COURSES,
        Permission.CREATE_COURSES,
        Permission.EDIT_COURSES,
        Permission.DELETE_COURSES,
        Permission.MANAGE_USERS,
        Permission.VIEW_ANALYTICS,
        Permission.GRADE_ASSIGNMENTS
      ]
    };

    return rolePermissions[state.user.role]?.includes(permission) || false;
  };

  const isRole = (role: UserRole): boolean => {
    return state.user?.role === role;
  };

  return (
    <AuthContext.Provider value={{
      state,
      login,
      register,
      logout,
      updateUser,
      hasPermission,
      isRole
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};