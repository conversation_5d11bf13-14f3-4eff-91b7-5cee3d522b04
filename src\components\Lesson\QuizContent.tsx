import React, { useState, useEffect } from 'react';
import { CheckCir<PERSON>, XCir<PERSON>, Clock, RotateCcw, Award } from 'lucide-react';
import { QuizContent as QuizContentType, QuizQuestion, QuestionType } from '../../types';

interface QuizContentProps {
  content: QuizContentType;
  onComplete: (score: number) => void;
  isCompleted: boolean;
}

const QuizContent: React.FC<QuizContentProps> = ({
  content,
  onComplete,
  isCompleted,
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [showResults, setShowResults] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [quizStarted, setQuizStarted] = useState(false);
  const [score, setScore] = useState(0);
  const [maxScore, setMaxScore] = useState(0);

  useEffect(() => {
    // Calculate max possible score
    const total = content.questions.reduce((sum, question) => sum + question.points, 0);
    setMaxScore(total);
  }, [content.questions]);

  useEffect(() => {
    if (content.timeLimit && quizStarted && !showResults && !isCompleted) {
      setTimeRemaining(content.timeLimit * 60); // Convert minutes to seconds
    }
  }, [content.timeLimit, quizStarted, showResults, isCompleted]);

  useEffect(() => {
    if (timeRemaining !== null && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0) {
      handleSubmitQuiz();
    }
  }, [timeRemaining]);

  const startQuiz = () => {
    setQuizStarted(true);
    setAnswers({});
    setCurrentQuestionIndex(0);
    setShowResults(false);
  };

  const handleAnswerChange = (questionId: string, answer: string | string[]) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const nextQuestion = () => {
    if (currentQuestionIndex < content.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSubmitQuiz = () => {
    let totalScore = 0;
    
    content.questions.forEach(question => {
      const userAnswer = answers[question.id];
      const isCorrect = checkAnswer(question, userAnswer);
      if (isCorrect) {
        totalScore += question.points;
      }
    });

    setScore(totalScore);
    setShowResults(true);
    
    const percentage = (totalScore / maxScore) * 100;
    if (percentage >= content.passingScore) {
      onComplete(percentage);
    }
  };

  const checkAnswer = (question: QuizQuestion, userAnswer: string | string[]): boolean => {
    if (!userAnswer) return false;

    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
      case QuestionType.TRUE_FALSE:
        return userAnswer === question.correctAnswer;
      case QuestionType.SHORT_ANSWER:
        if (typeof userAnswer === 'string' && typeof question.correctAnswer === 'string') {
          return userAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
        }
        return false;
      default:
        return false;
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderQuestion = (question: QuizQuestion) => {
    const userAnswer = answers[question.id];

    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return (
          <div className="space-y-3">
            {question.options?.map((option, index) => (
              <label
                key={index}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                  userAnswer === option
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <input
                  type="radio"
                  name={question.id}
                  value={option}
                  checked={userAnswer === option}
                  onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                  className="mr-3"
                />
                <span>{option}</span>
              </label>
            ))}
          </div>
        );

      case QuestionType.TRUE_FALSE:
        return (
          <div className="space-y-3">
            {['True', 'False'].map((option) => (
              <label
                key={option}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                  userAnswer === option
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <input
                  type="radio"
                  name={question.id}
                  value={option}
                  checked={userAnswer === option}
                  onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                  className="mr-3"
                />
                <span>{option}</span>
              </label>
            ))}
          </div>
        );

      case QuestionType.SHORT_ANSWER:
        return (
          <input
            type="text"
            value={userAnswer as string || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Enter your answer..."
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        );

      default:
        return <div>Unsupported question type</div>;
    }
  };

  const renderResults = () => {
    const percentage = (score / maxScore) * 100;
    const passed = percentage >= content.passingScore;

    return (
      <div className="text-center space-y-6">
        <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${
          passed ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
        }`}>
          {passed ? <Award className="w-10 h-10" /> : <XCircle className="w-10 h-10" />}
        </div>

        <div>
          <h3 className={`text-2xl font-bold ${passed ? 'text-green-600' : 'text-red-600'}`}>
            {passed ? 'Congratulations!' : 'Try Again'}
          </h3>
          <p className="text-gray-600 mt-2">
            You scored {score} out of {maxScore} points ({Math.round(percentage)}%)
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Passing score: {content.passingScore}%
          </p>
        </div>

        {/* Question-by-question results */}
        <div className="text-left space-y-4">
          <h4 className="font-semibold text-gray-900">Question Results:</h4>
          {content.questions.map((question, index) => {
            const userAnswer = answers[question.id];
            const isCorrect = checkAnswer(question, userAnswer);
            
            return (
              <div key={question.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className={`p-1 rounded-full ${
                    isCorrect ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                  }`}>
                    {isCorrect ? <CheckCircle className="w-4 h-4" /> : <XCircle className="w-4 h-4" />}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">
                      Question {index + 1}: {question.question}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      Your answer: {userAnswer || 'No answer'}
                    </p>
                    {!isCorrect && (
                      <p className="text-sm text-green-600 mt-1">
                        Correct answer: {question.correctAnswer}
                      </p>
                    )}
                    {question.explanation && (
                      <p className="text-sm text-blue-600 mt-2 italic">
                        {question.explanation}
                      </p>
                    )}
                  </div>
                  <span className="text-sm font-medium">
                    {isCorrect ? question.points : 0}/{question.points} pts
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {!passed && content.allowRetries && (
          <button
            onClick={startQuiz}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Retake Quiz</span>
          </button>
        )}
      </div>
    );
  };

  if (isCompleted) {
    return (
      <div className="p-6 text-center">
        <div className="flex items-center justify-center space-x-2 text-green-600 mb-4">
          <CheckCircle className="w-6 h-6" />
          <span className="text-lg font-medium">Quiz Completed!</span>
        </div>
        <p className="text-gray-600">
          You have successfully completed this quiz.
        </p>
      </div>
    );
  }

  if (!quizStarted) {
    return (
      <div className="p-6 text-center space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Quiz Instructions</h3>
          <div className="space-y-2 text-gray-600">
            <p>• {content.questions.length} questions</p>
            <p>• Passing score: {content.passingScore}%</p>
            {content.timeLimit && <p>• Time limit: {content.timeLimit} minutes</p>}
            <p>• {content.allowRetries ? 'Multiple attempts allowed' : 'Single attempt only'}</p>
          </div>
        </div>
        
        <button
          onClick={startQuiz}
          className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
        >
          Start Quiz
        </button>
      </div>
    );
  }

  if (showResults) {
    return <div className="p-6">{renderResults()}</div>;
  }

  const currentQuestion = content.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === content.questions.length - 1;
  const allQuestionsAnswered = content.questions.every(q => answers[q.id]);

  return (
    <div className="p-6">
      {/* Timer */}
      {timeRemaining !== null && (
        <div className="flex items-center justify-center mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <Clock className="w-5 h-5 text-yellow-600 mr-2" />
          <span className={`font-medium ${
            timeRemaining < 300 ? 'text-red-600' : 'text-yellow-600'
          }`}>
            Time remaining: {formatTime(timeRemaining)}
          </span>
        </div>
      )}

      {/* Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Question {currentQuestionIndex + 1} of {content.questions.length}
          </span>
          <span className="text-sm text-gray-600">
            {currentQuestion.points} point{currentQuestion.points !== 1 ? 's' : ''}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentQuestionIndex + 1) / content.questions.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Question */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {currentQuestion.question}
        </h3>
        {renderQuestion(currentQuestion)}
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <button
          onClick={previousQuestion}
          disabled={currentQuestionIndex === 0}
          className={`px-4 py-2 rounded-md transition-colors ${
            currentQuestionIndex === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Previous
        </button>

        <div className="flex space-x-2">
          {content.questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentQuestionIndex(index)}
              className={`w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                index === currentQuestionIndex
                  ? 'bg-blue-600 text-white'
                  : answers[content.questions[index].id]
                  ? 'bg-green-100 text-green-600'
                  : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
              }`}
            >
              {index + 1}
            </button>
          ))}
        </div>

        {isLastQuestion ? (
          <button
            onClick={handleSubmitQuiz}
            disabled={!allQuestionsAnswered}
            className={`px-6 py-2 rounded-md font-medium transition-colors ${
              allQuestionsAnswered
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Submit Quiz
          </button>
        ) : (
          <button
            onClick={nextQuestion}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Next
          </button>
        )}
      </div>
    </div>
  );
};

export default QuizContent;
