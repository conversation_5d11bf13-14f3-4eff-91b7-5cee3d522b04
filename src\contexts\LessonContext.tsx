import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Lesson, LessonState, UserProgress, QuizSubmission, AssignmentSubmission, ProgressStatus } from '../types';
import { useAuth } from './AuthContext';

interface LessonContextType {
  state: LessonState;
  loadLesson: (lessonId: string) => Promise<void>;
  completeLesson: (lessonId: string, score?: number) => Promise<void>;
  submitQuiz: (lessonId: string, answers: any[]) => Promise<void>;
  submitAssignment: (lessonId: string, submission: any) => Promise<void>;
  updateProgress: (lessonId: string, timeSpent: number) => void;
  canAccessLesson: (lesson: Lesson, userProgress: UserProgress[]) => boolean;
}

const LessonContext = createContext<LessonContextType | undefined>(undefined);

type LessonAction =
  | { type: 'LOAD_LESSON_START' }
  | { type: 'LOAD_LESSON_SUCCESS'; payload: Lesson }
  | { type: 'LOAD_LESSON_FAILURE'; payload: string }
  | { type: 'UPDATE_PROGRESS'; payload: UserProgress }
  | { type: 'ADD_SUBMISSION'; payload: QuizSubmission | AssignmentSubmission }
  | { type: 'SET_PROGRESS'; payload: UserProgress[] };

const lessonReducer = (state: LessonState, action: LessonAction): LessonState => {
  switch (action.type) {
    case 'LOAD_LESSON_START':
      return { ...state, isLoading: true, error: null };
    case 'LOAD_LESSON_SUCCESS':
      return { ...state, isLoading: false, activeLesson: action.payload, error: null };
    case 'LOAD_LESSON_FAILURE':
      return { ...state, isLoading: false, error: action.payload };
    case 'UPDATE_PROGRESS':
      return {
        ...state,
        progress: state.progress.map(p =>
          p.lessonId === action.payload.lessonId ? action.payload : p
        ),
      };
    case 'ADD_SUBMISSION':
      return { ...state, submissions: [...state.submissions, action.payload] };
    case 'SET_PROGRESS':
      return { ...state, progress: action.payload };
    default:
      return state;
  }
};

const initialState: LessonState = {
  activeLesson: null,
  progress: [],
  submissions: [],
  isLoading: false,
  error: null,
};

export const LessonProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(lessonReducer, initialState);
  const { state: authState } = useAuth();

  // Mock lesson data
  const mockLessons: Record<string, Lesson> = {
    '1': {
      id: '1',
      moduleId: 'module1',
      title: 'Introduction to React Components',
      description: 'Learn about React components and JSX',
      content: {
        type: 'text' as any,
        data: {
          html: '<h1>Welcome to React</h1><p>React is a JavaScript library for building user interfaces...</p>',
          attachments: [],
        },
      },
      order: 1,
      type: 'text' as any,
      estimatedDuration: 30,
      prerequisites: [],
      isPublished: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    '2': {
      id: '2',
      moduleId: 'module1',
      title: 'React Props and State',
      description: 'Understanding props and state in React',
      content: {
        type: 'video' as any,
        data: {
          videoUrl: 'https://example.com/video.mp4',
          transcript: 'In this video, we will learn about props and state...',
          attachments: [],
        },
      },
      order: 2,
      type: 'video' as any,
      estimatedDuration: 45,
      prerequisites: ['1'],
      isPublished: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    '3': {
      id: '3',
      moduleId: 'module1',
      title: 'React Quiz',
      description: 'Test your knowledge of React basics',
      content: {
        type: 'quiz' as any,
        data: {
          questions: [
            {
              id: 'q1',
              question: 'What is JSX?',
              type: 'multiple_choice' as any,
              options: [
                'A JavaScript extension',
                'A CSS framework',
                'A database',
                'A server technology'
              ],
              correctAnswer: 'A JavaScript extension',
              explanation: 'JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files.',
              points: 10,
            },
          ],
          passingScore: 70,
          allowRetries: true,
          timeLimit: 15,
        },
      },
      order: 3,
      type: 'quiz' as any,
      estimatedDuration: 15,
      prerequisites: ['1', '2'],
      isPublished: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  };

  const loadLesson = async (lessonId: string): Promise<void> => {
    dispatch({ type: 'LOAD_LESSON_START' });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const lesson = mockLessons[lessonId];
      if (lesson) {
        dispatch({ type: 'LOAD_LESSON_SUCCESS', payload: lesson });
      } else {
        dispatch({ type: 'LOAD_LESSON_FAILURE', payload: 'Lesson not found' });
      }
    } catch (error) {
      dispatch({ type: 'LOAD_LESSON_FAILURE', payload: 'Failed to load lesson' });
    }
  };

  const completeLesson = async (lessonId: string, score?: number): Promise<void> => {
    if (!authState.user) return;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const progress: UserProgress = {
        id: `progress-${Date.now()}`,
        userId: authState.user.id,
        courseId: 'course1', // This would come from context
        lessonId,
        status: ProgressStatus.COMPLETED,
        completedAt: new Date(),
        score,
        timeSpent: 0, // This would be tracked
        lastAccessedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      dispatch({ type: 'UPDATE_PROGRESS', payload: progress });
    } catch (error) {
      dispatch({ type: 'LOAD_LESSON_FAILURE', payload: 'Failed to complete lesson' });
    }
  };

  const submitQuiz = async (lessonId: string, answers: any[]): Promise<void> => {
    if (!authState.user) return;
    
    try {
      // Simulate API call and grading
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const lesson = mockLessons[lessonId];
      if (lesson && lesson.content.type === 'quiz') {
        const quizData = lesson.content.data as any;
        let totalScore = 0;
        let maxScore = 0;
        
        const gradedAnswers = answers.map((answer, index) => {
          const question = quizData.questions[index];
          const isCorrect = answer === question.correctAnswer;
          maxScore += question.points;
          if (isCorrect) totalScore += question.points;
          
          return {
            questionId: question.id,
            answer,
            isCorrect,
            points: isCorrect ? question.points : 0,
          };
        });
        
        const submission: QuizSubmission = {
          id: `quiz-${Date.now()}`,
          userId: authState.user.id,
          lessonId,
          answers: gradedAnswers,
          score: totalScore,
          maxScore,
          submittedAt: new Date(),
          timeSpent: 0, // This would be tracked
          passed: (totalScore / maxScore) * 100 >= quizData.passingScore,
        };
        
        dispatch({ type: 'ADD_SUBMISSION', payload: submission });
        
        // Complete the lesson if passed
        if (submission.passed) {
          await completeLesson(lessonId, (totalScore / maxScore) * 100);
        }
      }
    } catch (error) {
      dispatch({ type: 'LOAD_LESSON_FAILURE', payload: 'Failed to submit quiz' });
    }
  };

  const submitAssignment = async (lessonId: string, submission: any): Promise<void> => {
    if (!authState.user) return;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const assignmentSubmission: AssignmentSubmission = {
        id: `assignment-${Date.now()}`,
        userId: authState.user.id,
        lessonId,
        content: submission.content,
        fileUrl: submission.fileUrl,
        submittedAt: new Date(),
        maxScore: 100, // This would come from the assignment
        status: 'submitted' as any,
      };
      
      dispatch({ type: 'ADD_SUBMISSION', payload: assignmentSubmission });
    } catch (error) {
      dispatch({ type: 'LOAD_LESSON_FAILURE', payload: 'Failed to submit assignment' });
    }
  };

  const updateProgress = (lessonId: string, timeSpent: number): void => {
    if (!authState.user) return;
    
    const existingProgress = state.progress.find(p => p.lessonId === lessonId);
    
    const progress: UserProgress = {
      id: existingProgress?.id || `progress-${Date.now()}`,
      userId: authState.user.id,
      courseId: 'course1', // This would come from context
      lessonId,
      status: existingProgress?.status || ProgressStatus.IN_PROGRESS,
      completedAt: existingProgress?.completedAt,
      score: existingProgress?.score,
      timeSpent: (existingProgress?.timeSpent || 0) + timeSpent,
      lastAccessedAt: new Date(),
      createdAt: existingProgress?.createdAt || new Date(),
      updatedAt: new Date(),
    };
    
    dispatch({ type: 'UPDATE_PROGRESS', payload: progress });
  };

  const canAccessLesson = (lesson: Lesson, userProgress: UserProgress[]): boolean => {
    if (lesson.prerequisites.length === 0) return true;
    
    return lesson.prerequisites.every(prereqId => {
      const prereqProgress = userProgress.find(p => p.lessonId === prereqId);
      return prereqProgress?.status === ProgressStatus.COMPLETED;
    });
  };

  return (
    <LessonContext.Provider value={{
      state,
      loadLesson,
      completeLesson,
      submitQuiz,
      submitAssignment,
      updateProgress,
      canAccessLesson,
    }}>
      {children}
    </LessonContext.Provider>
  );
};

export const useLesson = (): LessonContextType => {
  const context = useContext(LessonContext);
  if (!context) {
    throw new Error('useLesson must be used within a LessonProvider');
  }
  return context;
};
